const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuration pour améliorer la compatibilité web
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Ajouter des extensions de fichiers pour le web
config.resolver.sourceExts.push('web.js', 'web.ts', 'web.tsx');

// Configuration pour les alias de modules
config.resolver.alias = {
  '@react-native-async-storage/async-storage': require.resolve('@react-native-async-storage/async-storage'),
};

module.exports = config;
