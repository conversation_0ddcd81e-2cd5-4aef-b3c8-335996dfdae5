{"version": 3, "sources": ["manualGesture.ts"], "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "_previous", "ManualGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "mappings": "AACA,SAASA,oBAAT,QAAqC,WAArC;;AAEA,SAASC,qBAAT,CACEC,OADF,EAEEC,SAFF,EAGE;AACA;;AACA,SAAOD,OAAP;AACD;;AAED,OAAO,MAAME,aAAN,SAA4BJ,oBAA5B,CAGL;AACAK,EAAAA,WAAW,GAAG;AACZ;AAEA,SAAKC,WAAL,GAAmB,sBAAnB;AACD;;AAEDC,EAAAA,QAAQ,CACNC,QADM,EAEN;AACA;AACA,SAAKC,QAAL,CAAcR,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAMM,QAAN,CAAeC,QAAf,CAAP;AACD;;AAbD", "sourcesContent": ["import { GestureUpdateEvent } from '../gestureHandlerCommon';\nimport { ContinousBaseGesture } from './gesture';\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<Record<string, never>>,\n  _previous?: GestureUpdateEvent<Record<string, never>>\n) {\n  'worklet';\n  return current;\n}\n\nexport class ManualGesture extends ContinousBaseGesture<\n  Record<string, never>,\n  Record<string, never>\n> {\n  constructor() {\n    super();\n\n    this.handlerName = 'ManualGestureHandler';\n  }\n\n  onChange(\n    callback: (event: GestureUpdateEvent<Record<string, never>>) => void\n  ) {\n    // @ts-ignore TS being overprotective, Record<string, never> is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type ManualGestureType = InstanceType<typeof ManualGesture>;\n"]}