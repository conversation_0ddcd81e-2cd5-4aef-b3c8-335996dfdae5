const Product = require('../models/Product');
const Order = require('../models/Order');
const Client = require('../models/Client');
const Admin = require('../models/Admin');
const StockHistory = require('../models/StockHistory');
const stockService = require('../services/stockService');
const notificationService = require('../services/notificationService');

class AdminController {

  // Tableau de bord - Statistiques générales
  async getDashboardStats(req, res) {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const startOfYear = new Date(today.getFullYear(), 0, 1);

      // Statistiques des commandes
      const [
        totalCommandes,
        commandesAujourdhui,
        commandesMois,
        commandesAnnee,
        chiffreAffaire<PERSON><PERSON>,
        chiffreAffaireAnnee
      ] = await Promise.all([
        Order.countDocuments(),
        Order.countDocuments({ dateCommande: { $gte: startOfDay } }),
        Order.countDocuments({ dateCommande: { $gte: startOfMonth } }),
        Order.countDocuments({ dateCommande: { $gte: startOfYear } }),
        Order.aggregate([
          { $match: { dateCommande: { $gte: startOfMonth }, statut: { $ne: 'annulee' } } },
          { $group: { _id: null, total: { $sum: '$montantTotal' } } }
        ]),
        Order.aggregate([
          { $match: { dateCommande: { $gte: startOfYear }, statut: { $ne: 'annulee' } } },
          { $group: { _id: null, total: { $sum: '$montantTotal' } } }
        ])
      ]);

      // Statistiques des produits et stock
      const [
        totalProduits,
        produitsActifs,
        produitsStockFaible,
        produitsRupture
      ] = await Promise.all([
        Product.countDocuments(),
        Product.countDocuments({ isActive: true }),
        Product.countDocuments({ 
          isActive: true, 
          $expr: { $lte: ['$stock', '$seuilAlerte'] } 
        }),
        Product.countDocuments({ isActive: true, stock: 0 })
      ]);

      // Statistiques des clients
      const [
        totalClients,
        clientsActifs,
        nouveauxClientsMois
      ] = await Promise.all([
        Client.countDocuments(),
        Client.countDocuments({ isActive: true }),
        Client.countDocuments({ 
          dateInscription: { $gte: startOfMonth },
          isActive: true 
        })
      ]);

      // Commandes par statut
      const commandesParStatut = await Order.aggregate([
        {
          $group: {
            _id: '$statut',
            count: { $sum: 1 }
          }
        }
      ]);

      // Évolution des ventes (7 derniers jours)
      const ventesParJour = await Order.aggregate([
        {
          $match: {
            dateCommande: { 
              $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
            },
            statut: { $ne: 'annulee' }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$dateCommande' }
            },
            ventes: { $sum: '$montantTotal' },
            commandes: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      res.json({
        commandes: {
          total: totalCommandes,
          aujourdhui: commandesAujourdhui,
          mois: commandesMois,
          annee: commandesAnnee,
          parStatut: commandesParStatut
        },
        chiffreAffaire: {
          mois: chiffreAffaireMois[0]?.total || 0,
          annee: chiffreAffaireAnnee[0]?.total || 0
        },
        produits: {
          total: totalProduits,
          actifs: produitsActifs,
          stockFaible: produitsStockFaible,
          rupture: produitsRupture
        },
        clients: {
          total: totalClients,
          actifs: clientsActifs,
          nouveauxMois: nouveauxClientsMois
        },
        ventesParJour
      });

    } catch (error) {
      console.error('Erreur récupération statistiques:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des statistiques',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Gestion des produits - Créer un produit
  async createProduct(req, res) {
    try {
      const productData = {
        ...req.body,
        ajouteParAdmin: req.user._id
      };

      const product = new Product(productData);
      await product.save();

      // Enregistrer l'ajout initial dans l'historique du stock
      if (product.stock > 0) {
        await StockHistory.enregistrerModification({
          produitId: product._id,
          nomProduit: product.nom,
          typeOperation: 'ajout_manuel',
          quantiteAvant: 0,
          quantiteApres: product.stock,
          raison: 'Stock initial lors de la création du produit',
          modifiePar: req.user._id
        });
      }

      const populatedProduct = await Product.findById(product._id)
        .populate('ajouteParAdmin', 'nom email');

      res.status(201).json({
        message: 'Produit créé avec succès',
        product: populatedProduct
      });

    } catch (error) {
      console.error('Erreur création produit:', error);
      res.status(500).json({
        message: 'Erreur lors de la création du produit',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Gestion des produits - Mettre à jour un produit
  async updateProduct(req, res) {
    try {
      const { id } = req.params;
      const updates = req.body;

      const product = await Product.findById(id);
      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      // Si le stock est modifié, enregistrer dans l'historique
      if (updates.stock !== undefined && updates.stock !== product.stock) {
        await StockHistory.enregistrerModification({
          produitId: product._id,
          nomProduit: product.nom,
          typeOperation: 'correction_inventaire',
          quantiteAvant: product.stock,
          quantiteApres: updates.stock,
          raison: 'Mise à jour manuelle du stock',
          modifiePar: req.user._id
        });
      }

      const updatedProduct = await Product.findByIdAndUpdate(
        id,
        { $set: updates },
        { new: true, runValidators: true }
      ).populate('ajouteParAdmin', 'nom email');

      // Vérifier si le stock est faible après la mise à jour
      if (updatedProduct.isLowStock()) {
        await stockService.alerterStockFaible(updatedProduct);
      }

      res.json({
        message: 'Produit mis à jour avec succès',
        product: updatedProduct
      });

    } catch (error) {
      console.error('Erreur mise à jour produit:', error);
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du produit',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Gestion des produits - Supprimer un produit (désactivation)
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;

      const product = await Product.findById(id);
      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      // Désactiver au lieu de supprimer
      product.isActive = false;
      await product.save();

      res.json({
        message: 'Produit désactivé avec succès',
        product
      });

    } catch (error) {
      console.error('Erreur suppression produit:', error);
      res.status(500).json({
        message: 'Erreur lors de la suppression du produit',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Gestion des commandes - Obtenir toutes les commandes
  async getAllOrders(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        statut, 
        clientId, 
        dateDebut, 
        dateFin 
      } = req.query;
      
      const skip = (page - 1) * limit;

      // Construction de la requête
      const query = {};
      if (statut) query.statut = statut;
      if (clientId) query.clientId = clientId;
      if (dateDebut || dateFin) {
        query.dateCommande = {};
        if (dateDebut) query.dateCommande.$gte = new Date(dateDebut);
        if (dateFin) query.dateCommande.$lte = new Date(dateFin);
      }

      const [orders, total] = await Promise.all([
        Order.find(query)
          .sort({ dateCommande: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .populate('clientId', 'nom email telephone')
          .populate('produits.produitId', 'nom image'),
        Order.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });

    } catch (error) {
      console.error('Erreur récupération commandes admin:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des commandes',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Gestion des commandes - Mettre à jour le statut d'une commande
  async updateOrderStatus(req, res) {
    try {
      const { id } = req.params;
      const { statut, commentaire, dateLivraisonPrevue } = req.body;

      const order = await Order.findById(id)
        .populate('clientId', 'nom email fcmToken');

      if (!order) {
        return res.status(404).json({
          message: 'Commande non trouvée'
        });
      }

      const ancienStatut = order.statut;

      // Mettre à jour le statut
      await order.changerStatut(statut, commentaire, req.user._id);

      // Mettre à jour la date de livraison prévue si fournie
      if (dateLivraisonPrevue) {
        order.dateLivraisonPrevue = new Date(dateLivraisonPrevue);
        await order.save();
      }

      // Envoyer une notification au client
      if (order.clientId && ancienStatut !== statut) {
        await notificationService.notifyOrderStatusChange(
          order.clientId, 
          order, 
          statut
        );
      }

      const updatedOrder = await Order.findById(id)
        .populate('clientId', 'nom email telephone')
        .populate('historiqueStatut.modifiePar', 'nom');

      res.json({
        message: 'Statut de commande mis à jour avec succès',
        order: updatedOrder
      });

    } catch (error) {
      console.error('Erreur mise à jour statut commande:', error);
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du statut',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
}

module.exports = new AdminController();
