{"name": "grosmarket-backend", "version": "1.0.0", "description": "Backend API pour l'application GrosMarket", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["grossiste", "api", "mongodb", "express"], "author": "GrosMarket Team", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "nodemailer": "^7.0.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}