{"version": 3, "sources": ["GestureHandlerWebDelegate.ts"], "names": ["findNodeHandle", "PointerEventManager", "State", "isPointerInBounds", "MouseB<PERSON>on", "KeyboardEventManager", "WheelEventManager", "GestureHandlerWebDelegate", "userSelect", "touchAction", "init", "viewRef", "handler", "Error", "handlerTag", "isInitialized", "<PERSON><PERSON><PERSON><PERSON>", "view", "defaultViewStyles", "style", "config", "setUserSelect", "enabled", "setTouchAction", "setContextMenu", "eventManagers", "push", "for<PERSON>ach", "manager", "attachEventManager", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "state", "ACTIVE", "cursor", "shouldDisableContextMenu", "enableContextMenu", "undefined", "isButtonInConfig", "RIGHT", "addContextMenuListeners", "addEventListener", "disableContextMenu", "removeContextMenuListeners", "removeEventListener", "e", "preventDefault", "stopPropagation", "isHandlerEnabled", "onEnabledChange", "registerListeners", "unregisterListeners", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "_view", "value"], "mappings": ";;AAAA,OAAOA,cAAP,MAA2B,sBAA3B;AAMA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,iBAAT,QAAkC,UAAlC;AAGA,SAASC,WAAT,QAA4B,qCAA5B;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AACA,OAAOC,iBAAP,MAA8B,qBAA9B;AAOA,OAAO,MAAMC,yBAAN,CAEP;AAAA;AAAA,2CAC0B,KAD1B;;AAAA;;AAAA;;AAAA,2CAKmD,EALnD;;AAAA,+CAMiD;AAC7CC,MAAAA,UAAU,EAAE,EADiC;AAE7CC,MAAAA,WAAW,EAAE;AAFgC,KANjD;AAAA;;AAWEC,EAAAA,IAAI,CAACC,OAAD,EAAkBC,OAAlB,EAAkD;AACpD,QAAI,CAACD,OAAL,EAAc;AACZ,YAAM,IAAIE,KAAJ,CACH,wCAAuCD,OAAO,CAACE,UAAW,EADvD,CAAN;AAGD;;AAED,SAAKC,aAAL,GAAqB,IAArB;AAEA,SAAKC,cAAL,GAAsBJ,OAAtB;AACA,SAAKK,IAAL,GAAYjB,cAAc,CAACW,OAAD,CAA1B;AAEA,SAAKO,iBAAL,GAAyB;AACvBV,MAAAA,UAAU,EAAE,KAAKS,IAAL,CAAUE,KAAV,CAAgBX,UADL;AAEvBC,MAAAA,WAAW,EAAE,KAAKQ,IAAL,CAAUE,KAAV,CAAgBV;AAFN,KAAzB;AAKA,UAAMW,MAAM,GAAGR,OAAO,CAACQ,MAAvB;AAEA,SAAKC,aAAL,CAAmBD,MAAM,CAACE,OAA1B;AACA,SAAKC,cAAL,CAAoBH,MAAM,CAACE,OAA3B;AACA,SAAKE,cAAL,CAAoBJ,MAAM,CAACE,OAA3B;AAEA,SAAKG,aAAL,CAAmBC,IAAnB,CAAwB,IAAIzB,mBAAJ,CAAwB,KAAKgB,IAA7B,CAAxB;AACA,SAAKQ,aAAL,CAAmBC,IAAnB,CAAwB,IAAIrB,oBAAJ,CAAyB,KAAKY,IAA9B,CAAxB;AACA,SAAKQ,aAAL,CAAmBC,IAAnB,CAAwB,IAAIpB,iBAAJ,CAAsB,KAAKW,IAA3B,CAAxB;AAEA,SAAKQ,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzB,KAAKZ,cAAL,CAAoBa,kBAApB,CAAuCD,OAAvC,CADF;AAGD;;AAEDzB,EAAAA,iBAAiB,CAAC;AAAE2B,IAAAA,CAAF;AAAKC,IAAAA;AAAL,GAAD,EAA8C;AAC7D,WAAO5B,iBAAiB,CAAC,KAAKc,IAAN,EAAY;AAAEa,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAAZ,CAAxB;AACD;;AAEDC,EAAAA,WAAW,GAAkB;AAC3B,UAAMC,IAAI,GAAG,KAAKhB,IAAL,CAAUiB,qBAAV,EAAb;AAEA,WAAO;AACLC,MAAAA,KAAK,EAAEF,IAAI,CAACG,IADP;AAELC,MAAAA,KAAK,EAAEJ,IAAI,CAACK,GAFP;AAGLC,MAAAA,KAAK,EAAEN,IAAI,CAACM,KAHP;AAILC,MAAAA,MAAM,EAAEP,IAAI,CAACO;AAJR,KAAP;AAMD;;AAEDC,EAAAA,KAAK,GAAS;AACZ,SAAKhB,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzBA,OAAO,CAACc,YAAR,EADF;AAGD;;AAEDC,EAAAA,cAAc,GAAG;AACf,UAAMvB,MAAM,GAAG,KAAKJ,cAAL,CAAoBI,MAAnC;;AAEA,QACEA,MAAM,CAACwB,YAAP,IACAxB,MAAM,CAACwB,YAAP,KAAwB,MADxB,IAEA,KAAK5B,cAAL,CAAoB6B,KAApB,KAA8B3C,KAAK,CAAC4C,MAHtC,EAIE;AACA,WAAK7B,IAAL,CAAUE,KAAV,CAAgB4B,MAAhB,GAAyB,MAAzB;AACD;AACF;;AAEOC,EAAAA,wBAAwB,CAAC5B,MAAD,EAAiB;AAC/C,WACGA,MAAM,CAAC6B,iBAAP,KAA6BC,SAA7B,IACC,KAAKlC,cAAL,CAAoBmC,gBAApB,CAAqC/C,WAAW,CAACgD,KAAjD,CADF,IAEAhC,MAAM,CAAC6B,iBAAP,KAA6B,KAH/B;AAKD;;AAEOI,EAAAA,uBAAuB,CAACjC,MAAD,EAAuB;AACpD,QAAI,KAAK4B,wBAAL,CAA8B5B,MAA9B,CAAJ,EAA2C;AACzC,WAAKH,IAAL,CAAUqC,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,kBAA/C;AACD,KAFD,MAEO,IAAInC,MAAM,CAAC6B,iBAAX,EAA8B;AACnC,WAAKhC,IAAL,CAAUqC,gBAAV,CAA2B,aAA3B,EAA0C,KAAKL,iBAA/C;AACD;AACF;;AAEOO,EAAAA,0BAA0B,CAACpC,MAAD,EAAuB;AACvD,QAAI,KAAK4B,wBAAL,CAA8B5B,MAA9B,CAAJ,EAA2C;AACzC,WAAKH,IAAL,CAAUwC,mBAAV,CAA8B,aAA9B,EAA6C,KAAKF,kBAAlD;AACD,KAFD,MAEO,IAAInC,MAAM,CAAC6B,iBAAX,EAA8B;AACnC,WAAKhC,IAAL,CAAUwC,mBAAV,CAA8B,aAA9B,EAA6C,KAAKR,iBAAlD;AACD;AACF;;AAEOM,EAAAA,kBAAkB,CAAaG,CAAb,EAAkC;AAC1DA,IAAAA,CAAC,CAACC,cAAF;AACD;;AAEOV,EAAAA,iBAAiB,CAAaS,CAAb,EAAkC;AACzDA,IAAAA,CAAC,CAACE,eAAF;AACD;;AAEOvC,EAAAA,aAAa,CAACwC,gBAAD,EAA4B;AAC/C,UAAM;AAAErD,MAAAA;AAAF,QAAiB,KAAKQ,cAAL,CAAoBI,MAA3C;AAEA,SAAKH,IAAL,CAAUE,KAAV,CAAgB,YAAhB,IAAgC0C,gBAAgB,GAC3CrD,UAD2C,aAC3CA,UAD2C,cAC3CA,UAD2C,GAC7B,MAD6B,GAE5C,KAAKU,iBAAL,CAAuBV,UAF3B;AAIA,SAAKS,IAAL,CAAUE,KAAV,CAAgB,kBAAhB,IAAsC0C,gBAAgB,GACjDrD,UADiD,aACjDA,UADiD,cACjDA,UADiD,GACnC,MADmC,GAElD,KAAKU,iBAAL,CAAuBV,UAF3B;AAGD;;AAEOe,EAAAA,cAAc,CAACsC,gBAAD,EAA4B;AAChD,UAAM;AAAEpD,MAAAA;AAAF,QAAkB,KAAKO,cAAL,CAAoBI,MAA5C;AAEA,SAAKH,IAAL,CAAUE,KAAV,CAAgB,aAAhB,IAAiC0C,gBAAgB,GAC5CpD,WAD4C,aAC5CA,WAD4C,cAC5CA,WAD4C,GAC7B,MAD6B,GAE7C,KAAKS,iBAAL,CAAuBT,WAF3B,CAHgD,CAOhD;;AACA,SAAKQ,IAAL,CAAUE,KAAV,CAAgB,oBAAhB,IAAwC0C,gBAAgB,GACnDpD,WADmD,aACnDA,WADmD,cACnDA,WADmD,GACpC,MADoC,GAEpD,KAAKS,iBAAL,CAAuBT,WAF3B;AAGD;;AAEOe,EAAAA,cAAc,CAACqC,gBAAD,EAA4B;AAChD,UAAMzC,MAAM,GAAG,KAAKJ,cAAL,CAAoBI,MAAnC;;AAEA,QAAIyC,gBAAJ,EAAsB;AACpB,WAAKR,uBAAL,CAA6BjC,MAA7B;AACD,KAFD,MAEO;AACL,WAAKoC,0BAAL,CAAgCpC,MAAhC;AACD;AACF;;AAED0C,EAAAA,eAAe,CAACxC,OAAD,EAAyB;AACtC,QAAI,CAAC,KAAKP,aAAV,EAAyB;AACvB;AACD;;AAED,SAAKM,aAAL,CAAmBC,OAAnB;AACA,SAAKC,cAAL,CAAoBD,OAApB;AACA,SAAKE,cAAL,CAAoBF,OAApB;;AAEA,QAAIA,OAAJ,EAAa;AACX,WAAKG,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IAAa;AACtC;AACA;AACA;AACA;AACA;AACAA,QAAAA,OAAO,CAACmC,iBAAR;AACD,OAPD;AAQD,KATD,MASO;AACL,WAAKtC,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IAAa;AACtCA,QAAAA,OAAO,CAACoC,mBAAR;AACD,OAFD;AAGD;AACF;;AAEDC,EAAAA,OAAO,GAAS,CACd;AACD;;AAEDC,EAAAA,UAAU,GAAS;AACjB,UAAM9C,MAAM,GAAG,KAAKJ,cAAL,CAAoBI,MAAnC;;AAEA,QACE,CAAC,CAAC,KAAKH,IAAL,CAAUE,KAAV,CAAgB4B,MAAjB,IAA2B,KAAK9B,IAAL,CAAUE,KAAV,CAAgB4B,MAAhB,KAA2B,MAAvD,KACA3B,MAAM,CAACwB,YAFT,EAGE;AACA,WAAK3B,IAAL,CAAUE,KAAV,CAAgB4B,MAAhB,GAAyB3B,MAAM,CAACwB,YAAhC;AACD;AACF;;AAEDuB,EAAAA,KAAK,GAAS;AACZ,SAAKxB,cAAL;AACD;;AAEDyB,EAAAA,QAAQ,GAAS;AACf,SAAKzB,cAAL;AACD;;AAED0B,EAAAA,MAAM,GAAS;AACb,SAAK1B,cAAL;AACD;;AAEM2B,EAAAA,OAAO,CAAClD,MAAD,EAAuB;AACnC,SAAKoC,0BAAL,CAAgCpC,MAAhC;AAEA,SAAKK,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IAAa;AACtCA,MAAAA,OAAO,CAACoC,mBAAR;AACD,KAFD;AAGD;;AAEc,MAAJ/C,IAAI,GAAG;AAChB,WAAO,KAAKsD,KAAZ;AACD;;AACc,MAAJtD,IAAI,CAACuD,KAAD,EAAqB;AAClC,SAAKD,KAAL,GAAaC,KAAb;AACD;;AAhNH", "sourcesContent": ["import findNodeHandle from '../../findNodeHandle';\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport {\n  GestureHandlerDelegate,\n  MeasureResult,\n} from './GestureHandlerDelegate';\nimport PointerEventManager from './PointerEventManager';\nimport { State } from '../../State';\nimport { isPointerInBounds } from '../utils';\nimport EventManager from './EventManager';\nimport { Config } from '../interfaces';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport KeyboardEventManager from './KeyboardEventManager';\nimport WheelEventManager from './WheelEventManager';\n\ninterface DefaultViewStyles {\n  userSelect: string;\n  touchAction: string;\n}\n\nexport class GestureHandlerWebDelegate\n  implements GestureHandlerDelegate<HTMLElement, IGestureHandler>\n{\n  private isInitialized = false;\n  private _view!: HTMLElement;\n\n  private gestureHandler!: IGestureHandler;\n  private eventManagers: EventManager<unknown>[] = [];\n  private defaultViewStyles: DefaultViewStyles = {\n    userSelect: '',\n    touchAction: '',\n  };\n\n  init(viewRef: number, handler: IGestureHandler): void {\n    if (!viewRef) {\n      throw new Error(\n        `Cannot find HTML Element for handler ${handler.handlerTag}`\n      );\n    }\n\n    this.isInitialized = true;\n\n    this.gestureHandler = handler;\n    this.view = findNodeHandle(viewRef) as unknown as HTMLElement;\n\n    this.defaultViewStyles = {\n      userSelect: this.view.style.userSelect,\n      touchAction: this.view.style.touchAction,\n    };\n\n    const config = handler.config;\n\n    this.setUserSelect(config.enabled);\n    this.setTouchAction(config.enabled);\n    this.setContextMenu(config.enabled);\n\n    this.eventManagers.push(new PointerEventManager(this.view));\n    this.eventManagers.push(new KeyboardEventManager(this.view));\n    this.eventManagers.push(new WheelEventManager(this.view));\n\n    this.eventManagers.forEach((manager) =>\n      this.gestureHandler.attachEventManager(manager)\n    );\n  }\n\n  isPointerInBounds({ x, y }: { x: number; y: number }): boolean {\n    return isPointerInBounds(this.view, { x, y });\n  }\n\n  measureView(): MeasureResult {\n    const rect = this.view.getBoundingClientRect();\n\n    return {\n      pageX: rect.left,\n      pageY: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }\n\n  reset(): void {\n    this.eventManagers.forEach((manager: EventManager<unknown>) =>\n      manager.resetManager()\n    );\n  }\n\n  tryResetCursor() {\n    const config = this.gestureHandler.config;\n\n    if (\n      config.activeCursor &&\n      config.activeCursor !== 'auto' &&\n      this.gestureHandler.state === State.ACTIVE\n    ) {\n      this.view.style.cursor = 'auto';\n    }\n  }\n\n  private shouldDisableContextMenu(config: Config) {\n    return (\n      (config.enableContextMenu === undefined &&\n        this.gestureHandler.isButtonInConfig(MouseButton.RIGHT)) ||\n      config.enableContextMenu === false\n    );\n  }\n\n  private addContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.addEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.addEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private removeContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.removeEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.removeEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private disableContextMenu(this: void, e: MouseEvent): void {\n    e.preventDefault();\n  }\n\n  private enableContextMenu(this: void, e: MouseEvent): void {\n    e.stopPropagation();\n  }\n\n  private setUserSelect(isHandlerEnabled: boolean) {\n    const { userSelect } = this.gestureHandler.config;\n\n    this.view.style['userSelect'] = isHandlerEnabled\n      ? (userSelect ?? 'none')\n      : this.defaultViewStyles.userSelect;\n\n    this.view.style['webkitUserSelect'] = isHandlerEnabled\n      ? (userSelect ?? 'none')\n      : this.defaultViewStyles.userSelect;\n  }\n\n  private setTouchAction(isHandlerEnabled: boolean) {\n    const { touchAction } = this.gestureHandler.config;\n\n    this.view.style['touchAction'] = isHandlerEnabled\n      ? (touchAction ?? 'none')\n      : this.defaultViewStyles.touchAction;\n\n    // @ts-ignore This one disables default events on Safari\n    this.view.style['WebkitTouchCallout'] = isHandlerEnabled\n      ? (touchAction ?? 'none')\n      : this.defaultViewStyles.touchAction;\n  }\n\n  private setContextMenu(isHandlerEnabled: boolean) {\n    const config = this.gestureHandler.config;\n\n    if (isHandlerEnabled) {\n      this.addContextMenuListeners(config);\n    } else {\n      this.removeContextMenuListeners(config);\n    }\n  }\n\n  onEnabledChange(enabled: boolean): void {\n    if (!this.isInitialized) {\n      return;\n    }\n\n    this.setUserSelect(enabled);\n    this.setTouchAction(enabled);\n    this.setContextMenu(enabled);\n\n    if (enabled) {\n      this.eventManagers.forEach((manager) => {\n        // It may look like managers will be registered twice when handler is mounted for the first time.\n        // However, `init` method is called AFTER `updateGestureConfig` - it means that delegate has not\n        // been initialized yet, so this code won't be executed.\n        //\n        // Also, because we use defined functions, not lambdas, they will not be registered multiple times.\n        manager.registerListeners();\n      });\n    } else {\n      this.eventManagers.forEach((manager) => {\n        manager.unregisterListeners();\n      });\n    }\n  }\n\n  onBegin(): void {\n    // no-op for now\n  }\n\n  onActivate(): void {\n    const config = this.gestureHandler.config;\n\n    if (\n      (!this.view.style.cursor || this.view.style.cursor === 'auto') &&\n      config.activeCursor\n    ) {\n      this.view.style.cursor = config.activeCursor;\n    }\n  }\n\n  onEnd(): void {\n    this.tryResetCursor();\n  }\n\n  onCancel(): void {\n    this.tryResetCursor();\n  }\n\n  onFail(): void {\n    this.tryResetCursor();\n  }\n\n  public destroy(config: Config): void {\n    this.removeContextMenuListeners(config);\n\n    this.eventManagers.forEach((manager) => {\n      manager.unregisterListeners();\n    });\n  }\n\n  public get view() {\n    return this._view;\n  }\n  public set view(value: HTMLElement) {\n    this._view = value;\n  }\n}\n"]}