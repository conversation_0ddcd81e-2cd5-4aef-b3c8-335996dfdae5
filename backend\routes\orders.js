const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { validate, schemas } = require('../middleware/validation');
const { authenticateClient } = require('../middleware/auth');

// Toutes les routes nécessitent une authentification client
router.use(authenticateClient);

// Créer une nouvelle commande
router.post('/', 
  validate(schemas.order), 
  orderController.createOrder
);

// Obtenir toutes les commandes du client
router.get('/', orderController.getClientOrders);

// Obtenir les statistiques des commandes du client
router.get('/stats', orderController.getClientOrderStats);

// Obtenir une commande spécifique
router.get('/:id', orderController.getClientOrderById);

// Obtenir le statut d'une commande
router.get('/:id/status', orderController.getOrderStatus);

// Annuler une commande
router.put('/:id/cancel', orderController.cancelOrder);

module.exports = router;
