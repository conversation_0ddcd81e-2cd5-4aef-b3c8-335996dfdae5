const Product = require('../models/Product');
const StockHistory = require('../models/StockHistory');
const Admin = require('../models/Admin');
const notificationService = require('./notificationService');

class StockService {
  
  // Vérifier et mettre à jour le stock pour une commande
  async verifierEtReduireStock(produits, commandeId) {
    const session = await Product.startSession();
    session.startTransaction();

    try {
      const stockUpdates = [];
      const produitsIndisponibles = [];

      // Vérifier la disponibilité de tous les produits
      for (const item of produits) {
        const produit = await Product.findById(item.produitId).session(session);
        
        if (!produit) {
          throw new Error(`Produit ${item.produitId} non trouvé`);
        }

        if (!produit.isActive) {
          produitsIndisponibles.push({
            produitId: item.produitId,
            nom: produit.nom,
            raison: 'Produit inactif'
          });
          continue;
        }

        if (produit.stock < item.quantite) {
          produitsIndisponibles.push({
            produitId: item.produitId,
            nom: produit.nom,
            stockDisponible: produit.stock,
            quantiteDemandee: item.quantite,
            raison: 'Stock insuffisant'
          });
        }
      }

      // Si des produits sont indisponibles, annuler la transaction
      if (produitsIndisponibles.length > 0) {
        await session.abortTransaction();
        return {
          success: false,
          message: 'Certains produits ne sont pas disponibles',
          produitsIndisponibles
        };
      }

      // Réduire le stock de tous les produits
      for (const item of produits) {
        const produit = await Product.findById(item.produitId).session(session);
        const stockAvant = produit.stock;
        
        produit.stock -= item.quantite;
        await produit.save({ session });

        // Enregistrer l'historique
        await StockHistory.enregistrerModification({
          produitId: produit._id,
          nomProduit: produit.nom,
          typeOperation: 'reduction_commande',
          quantiteAvant: stockAvant,
          quantiteApres: produit.stock,
          raison: `Réduction pour commande`,
          reference: commandeId,
          commandeId: commandeId
        });

        stockUpdates.push({
          produitId: produit._id,
          nom: produit.nom,
          stockAvant,
          stockApres: produit.stock,
          quantiteReduite: item.quantite
        });

        // Vérifier si le stock est faible
        if (produit.isLowStock()) {
          await this.alerterStockFaible(produit);
        }
      }

      await session.commitTransaction();
      
      return {
        success: true,
        message: 'Stock mis à jour avec succès',
        stockUpdates
      };

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Restaurer le stock en cas d'annulation de commande
  async restaurerStock(produits, commandeId, raison = 'Annulation de commande') {
    const session = await Product.startSession();
    session.startTransaction();

    try {
      const stockUpdates = [];

      for (const item of produits) {
        const produit = await Product.findById(item.produitId).session(session);
        
        if (!produit) {
          console.warn(`Produit ${item.produitId} non trouvé lors de la restauration`);
          continue;
        }

        const stockAvant = produit.stock;
        produit.stock += item.quantite;
        await produit.save({ session });

        // Enregistrer l'historique
        await StockHistory.enregistrerModification({
          produitId: produit._id,
          nomProduit: produit.nom,
          typeOperation: 'retour_client',
          quantiteAvant: stockAvant,
          quantiteApres: produit.stock,
          raison,
          reference: commandeId,
          commandeId: commandeId
        });

        stockUpdates.push({
          produitId: produit._id,
          nom: produit.nom,
          stockAvant,
          stockApres: produit.stock,
          quantiteRestauree: item.quantite
        });
      }

      await session.commitTransaction();
      
      return {
        success: true,
        message: 'Stock restauré avec succès',
        stockUpdates
      };

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Mettre à jour le stock manuellement (admin)
  async mettreAJourStockManuel(produitId, nouvelleQuantite, typeOperation, raison, adminId, reference = null) {
    try {
      const produit = await Product.findById(produitId);
      
      if (!produit) {
        throw new Error('Produit non trouvé');
      }

      const stockAvant = produit.stock;
      produit.stock = nouvelleQuantite;
      await produit.save();

      // Enregistrer l'historique
      await StockHistory.enregistrerModification({
        produitId: produit._id,
        nomProduit: produit.nom,
        typeOperation,
        quantiteAvant: stockAvant,
        quantiteApres: nouvelleQuantite,
        raison,
        reference,
        modifiePar: adminId
      });

      // Vérifier si le stock est faible après la mise à jour
      if (produit.isLowStock()) {
        await this.alerterStockFaible(produit);
      }

      return {
        success: true,
        message: 'Stock mis à jour avec succès',
        produit: {
          id: produit._id,
          nom: produit.nom,
          stockAvant,
          stockApres: nouvelleQuantite,
          difference: nouvelleQuantite - stockAvant
        }
      };

    } catch (error) {
      throw error;
    }
  }

  // Alerter en cas de stock faible
  async alerterStockFaible(produit) {
    try {
      // Récupérer tous les admins actifs
      const admins = await Admin.find({ isActive: true });
      
      if (admins.length > 0) {
        await notificationService.notifyLowStock(produit, admins);
        console.log(`🔔 Alerte stock faible envoyée pour ${produit.nom}`);
      }
    } catch (error) {
      console.error('❌ Erreur envoi alerte stock faible:', error.message);
    }
  }

  // Obtenir les produits avec stock faible
  async obtenirProduitsStockFaible() {
    try {
      const produits = await Product.find({
        isActive: true,
        $expr: { $lte: ['$stock', '$seuilAlerte'] }
      }).sort({ stock: 1 });

      return produits;
    } catch (error) {
      throw error;
    }
  }

  // Obtenir les statistiques de stock
  async obtenirStatistiquesStock() {
    try {
      const stats = await Product.aggregate([
        {
          $match: { isActive: true }
        },
        {
          $group: {
            _id: null,
            totalProduits: { $sum: 1 },
            stockTotal: { $sum: '$stock' },
            valeurTotaleStock: { $sum: { $multiply: ['$stock', '$prix'] } },
            produitsStockFaible: {
              $sum: {
                $cond: [{ $lte: ['$stock', '$seuilAlerte'] }, 1, 0]
              }
            },
            produitsRupture: {
              $sum: {
                $cond: [{ $eq: ['$stock', 0] }, 1, 0]
              }
            }
          }
        }
      ]);

      const statsParCategorie = await Product.aggregate([
        {
          $match: { isActive: true }
        },
        {
          $group: {
            _id: '$categorie',
            nombreProduits: { $sum: 1 },
            stockTotal: { $sum: '$stock' },
            valeurStock: { $sum: { $multiply: ['$stock', '$prix'] } }
          }
        },
        {
          $sort: { valeurStock: -1 }
        }
      ]);

      return {
        global: stats[0] || {
          totalProduits: 0,
          stockTotal: 0,
          valeurTotaleStock: 0,
          produitsStockFaible: 0,
          produitsRupture: 0
        },
        parCategorie: statsParCategorie
      };
    } catch (error) {
      throw error;
    }
  }

  // Vérifier la cohérence du stock
  async verifierCoherenceStock() {
    try {
      const produitsInconsistants = await Product.find({
        $or: [
          { stock: { $lt: 0 } },
          { seuilAlerte: { $lt: 0 } },
          { prix: { $lte: 0 } }
        ]
      });

      return {
        inconsistances: produitsInconsistants.length,
        produits: produitsInconsistants
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new StockService();
