{"version": 3, "sources": ["GestureHandlerRootView.web.tsx"], "names": ["React", "View", "StyleSheet", "GestureHandlerRootViewContext", "GestureHandlerRootView", "style", "rest", "styles", "container", "create", "flex"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,SAASC,IAAT,EAA0BC,UAA1B,QAA4C,cAA5C;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AAKA,eAAe,SAASC,sBAAT,CAAgC;AAC7CC,EAAAA,KAD6C;AAE7C,KAAGC;AAF0C,CAAhC,EAGiB;AAC9B,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,IAAD;AAAM,IAAA,KAAK,EAAED,KAAF,aAAEA,KAAF,cAAEA,KAAF,GAAWE,MAAM,CAACC;AAA7B,KAA4CF,IAA5C,EADF,CADF;AAKD;AAED,MAAMC,MAAM,GAAGL,UAAU,CAACO,MAAX,CAAkB;AAC/BD,EAAAA,SAAS,EAAE;AAAEE,IAAAA,IAAI,EAAE;AAAR;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps, StyleSheet } from 'react-native';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView({\n  style,\n  ...rest\n}: GestureHandlerRootViewProps) {\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View style={style ?? styles.container} {...rest} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: { flex: 1 },\n});\n"]}