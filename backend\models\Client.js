const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const clientSchema = new mongoose.Schema({
  nom: {
    type: String,
    required: [true, 'Le nom est requis'],
    trim: true,
    maxlength: [100, 'Le nom ne peut pas dépasser 100 caractères']
  },
  email: {
    type: String,
    required: [true, 'L\'email est requis'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email invalide']
  },
  telephone: {
    type: String,
    required: [true, 'Le téléphone est requis'],
    trim: true,
    match: [/^[0-9+\-\s()]+$/, 'Numéro de téléphone invalide']
  },
  adresse: {
    rue: {
      type: String,
      required: [true, 'La rue est requise'],
      trim: true
    },
    ville: {
      type: String,
      required: [true, 'La ville est requise'],
      trim: true
    },
    codePostal: {
      type: String,
      required: [true, 'Le code postal est requis'],
      trim: true
    },
    pays: {
      type: String,
      default: 'France',
      trim: true
    }
  },
  motDePasse: {
    type: String,
    required: [true, 'Le mot de passe est requis'],
    minlength: [6, 'Le mot de passe doit contenir au moins 6 caractères']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  fcmToken: {
    type: String,
    default: null
  },
  dateInscription: {
    type: Date,
    default: Date.now
  },
  derniereConnexion: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
clientSchema.index({ email: 1 });
clientSchema.index({ telephone: 1 });
clientSchema.index({ 'adresse.ville': 1 });

// Middleware pour hasher le mot de passe avant sauvegarde
clientSchema.pre('save', async function(next) {
  if (!this.isModified('motDePasse')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.motDePasse = await bcrypt.hash(this.motDePasse, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Méthode pour comparer les mots de passe
clientSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.motDePasse);
};

// Méthode pour obtenir les informations publiques du client
clientSchema.methods.toPublicJSON = function() {
  const client = this.toObject();
  delete client.motDePasse;
  delete client.fcmToken;
  return client;
};

// Méthode pour mettre à jour la dernière connexion
clientSchema.methods.updateLastLogin = function() {
  this.derniereConnexion = new Date();
  return this.save();
};

module.exports = mongoose.model('Client', clientSchema);
