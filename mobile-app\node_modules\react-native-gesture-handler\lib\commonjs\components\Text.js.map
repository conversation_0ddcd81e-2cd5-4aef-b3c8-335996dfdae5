{"version": 3, "sources": ["Text.tsx"], "names": ["Text", "props", "ref", "onPress", "onLongPress", "rest", "textRef", "native", "Gesture", "Native", "runOnJS", "ref<PERSON><PERSON><PERSON>", "node", "current", "rngh", "Platform", "OS", "textElement", "setAttribute"], "mappings": ";;;;;;;AAAA;;AAOA;;AAMA;;AACA;;;;;;;;AAEO,MAAMA,IAAI,gBAAG,uBAClB,CAACC,KAAD,EAAqBC,GAArB,KAAmD;AACjD,QAAM;AAAEC,IAAAA,OAAF;AAAWC,IAAAA,WAAX;AAAwB,OAAGC;AAA3B,MAAoCJ,KAA1C;AAEA,QAAMK,OAAO,GAAG,mBAAsB,IAAtB,CAAhB;;AACA,QAAMC,MAAM,GAAGC,+BAAQC,MAAR,GAAiBC,OAAjB,CAAyB,IAAzB,CAAf;;AAEA,QAAMC,UAAU,GAAIC,IAAD,IAAe;AAChCN,IAAAA,OAAO,CAACO,OAAR,GAAkBD,IAAlB;;AAEA,QAAIV,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACD;;AAED,QAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;AAC7BA,MAAAA,GAAG,CAACU,IAAD,CAAH;AACD,KAFD,MAEO;AACLV,MAAAA,GAAG,CAACW,OAAJ,GAAcD,IAAd;AACD;AACF,GAZD,CANiD,CAoBjD;AACA;AACA;;;AACAD,EAAAA,UAAU,CAACG,IAAX,GAAkB,IAAlB;AAEA,wBAAU,MAAM;AACd,QAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,UAAMC,WAAW,GAAGf,GAAG,GAClBA,GAAD,CAA2BW,OADR,GAEnBP,OAAO,CAACO,OAFZ,CALc,CASd;;AACCI,IAAAA,WAAD,aAACA,WAAD,uBAACA,WAAD,CAA4CC,YAA5C,CACE,UADF,EAEE,MAFF;AAID,GAdD,EAcG,EAdH;AAgBA,SAAOf,OAAO,IAAIC,WAAX,gBACL,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAEG;AAA1B,kBACE,6BAAC,iBAAD;AACE,IAAA,OAAO,EAAEJ,OADX;AAEE,IAAA,WAAW,EAAEC,WAFf;AAGE,IAAA,GAAG,EAAEO;AAHP,KAIMN,IAJN,EADF,CADK,gBAUL,6BAAC,iBAAD;AAAQ,IAAA,GAAG,EAAEH;AAAb,KAAsBG,IAAtB,EAVF;AAYD,CAtDiB,CAAb,C,CAwDP", "sourcesContent": ["import React, {\n  ForwardedRef,\n  forwardRef,\n  RefObject,\n  useEffect,\n  useRef,\n} from 'react';\nimport {\n  Platform,\n  Text as RNText,\n  TextProps as RNTextProps,\n} from 'react-native';\n\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\n\nexport const Text = forwardRef(\n  (props: RNTextProps, ref: ForwardedRef<RNText>) => {\n    const { onPress, onLongPress, ...rest } = props;\n\n    const textRef = useRef<RNText | null>(null);\n    const native = Gesture.Native().runOnJS(true);\n\n    const refHandler = (node: any) => {\n      textRef.current = node;\n\n      if (ref === null) {\n        return;\n      }\n\n      if (typeof ref === 'function') {\n        ref(node);\n      } else {\n        ref.current = node;\n      }\n    };\n\n    // This is a special case for `Text` component. After https://github.com/software-mansion/react-native-gesture-handler/pull/3379 we check for\n    // `displayName` field. However, `Text` from RN has this field set to `Text`, but is also present in `RNSVGElements` set.\n    // We don't want to treat our `Text` as the one from `SVG`, therefore we add special field to ref.\n    refHandler.rngh = true;\n\n    useEffect(() => {\n      if (Platform.OS !== 'web') {\n        return;\n      }\n\n      const textElement = ref\n        ? (ref as RefObject<RNText>).current\n        : textRef.current;\n\n      // At this point we are sure that textElement is div in HTML tree\n      (textElement as unknown as HTMLDivElement)?.setAttribute(\n        'rnghtext',\n        'true'\n      );\n    }, []);\n\n    return onPress || onLongPress ? (\n      <GestureDetector gesture={native}>\n        <RNText\n          onPress={onPress}\n          onLongPress={onLongPress}\n          ref={refHandler}\n          {...rest}\n        />\n      </GestureDetector>\n    ) : (\n      <RNText ref={ref} {...rest} />\n    );\n  }\n);\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type Text = typeof Text & RNText;\n"]}