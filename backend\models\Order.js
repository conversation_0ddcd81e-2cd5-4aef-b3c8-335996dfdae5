const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  numeroCommande: {
    type: String,
    unique: true,
    required: true
  },
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    required: [true, 'L\'ID du client est requis']
  },
  produits: [{
    produitId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    nom: {
      type: String,
      required: true
    },
    prix: {
      type: Number,
      required: true,
      min: 0
    },
    quantite: {
      type: Number,
      required: true,
      min: 1
    },
    unite: {
      type: String,
      required: true
    },
    sousTotal: {
      type: Number,
      required: true,
      min: 0
    }
  }],
  statut: {
    type: String,
    enum: [
      'en_attente',
      'confirmee',
      'en_preparation',
      'en_livraison',
      'livree',
      'annulee',
      'retournee'
    ],
    default: 'en_attente'
  },
  adresseLivraison: {
    nom: {
      type: String,
      required: true
    },
    telephone: {
      type: String,
      required: true
    },
    rue: {
      type: String,
      required: true
    },
    ville: {
      type: String,
      required: true
    },
    codePostal: {
      type: String,
      required: true
    },
    pays: {
      type: String,
      default: 'France'
    },
    instructions: String
  },
  montantTotal: {
    type: Number,
    required: true,
    min: 0
  },
  fraisLivraison: {
    type: Number,
    default: 0,
    min: 0
  },
  dateCommande: {
    type: Date,
    default: Date.now
  },
  dateLivraisonPrevue: {
    type: Date
  },
  dateLivraisonReelle: {
    type: Date
  },
  modePaiement: {
    type: String,
    enum: ['livraison', 'especes', 'cheque'],
    default: 'livraison'
  },
  notes: {
    type: String,
    maxlength: 500
  },
  historiqueStatut: [{
    statut: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    commentaire: String,
    modifiePar: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin'
    }
  }],
  livreur: {
    nom: String,
    telephone: String
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
orderSchema.index({ numeroCommande: 1 });
orderSchema.index({ clientId: 1 });
orderSchema.index({ statut: 1 });
orderSchema.index({ dateCommande: -1 });
orderSchema.index({ 'adresseLivraison.ville': 1 });

// Middleware pour générer le numéro de commande
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.numeroCommande) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Compter les commandes du jour
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
    
    const count = await this.constructor.countDocuments({
      dateCommande: { $gte: startOfDay, $lt: endOfDay }
    });
    
    const sequence = String(count + 1).padStart(4, '0');
    this.numeroCommande = `CMD${year}${month}${day}${sequence}`;
  }
  next();
});

// Middleware pour ajouter l'historique de statut
orderSchema.pre('save', function(next) {
  if (this.isModified('statut') && !this.isNew) {
    this.historiqueStatut.push({
      statut: this.statut,
      date: new Date()
    });
  } else if (this.isNew) {
    this.historiqueStatut.push({
      statut: this.statut,
      date: new Date()
    });
  }
  next();
});

// Méthode pour calculer le montant total
orderSchema.methods.calculerMontantTotal = function() {
  const sousTotal = this.produits.reduce((total, produit) => {
    return total + produit.sousTotal;
  }, 0);
  this.montantTotal = sousTotal + this.fraisLivraison;
  return this.montantTotal;
};

// Méthode pour changer le statut
orderSchema.methods.changerStatut = function(nouveauStatut, commentaire, adminId) {
  this.statut = nouveauStatut;
  
  if (nouveauStatut === 'livree') {
    this.dateLivraisonReelle = new Date();
  }
  
  // L'historique sera ajouté automatiquement par le middleware pre('save')
  if (commentaire || adminId) {
    const dernierHistorique = this.historiqueStatut[this.historiqueStatut.length - 1];
    if (dernierHistorique) {
      dernierHistorique.commentaire = commentaire;
      dernierHistorique.modifiePar = adminId;
    }
  }
  
  return this.save();
};

// Méthode pour vérifier si la commande peut être annulée
orderSchema.methods.peutEtreAnnulee = function() {
  return ['en_attente', 'confirmee'].includes(this.statut);
};

// Virtual pour le délai de livraison
orderSchema.virtual('delaiLivraison').get(function() {
  if (this.dateLivraisonReelle && this.dateCommande) {
    return Math.ceil((this.dateLivraisonReelle - this.dateCommande) / (1000 * 60 * 60 * 24));
  }
  return null;
});

// Virtual pour le statut de livraison
orderSchema.virtual('statutLivraison').get(function() {
  if (this.statut === 'livree') return 'livree';
  if (this.dateLivraisonPrevue && new Date() > this.dateLivraisonPrevue) return 'retard';
  return 'dans_les_temps';
});

// Inclure les virtuals dans la sérialisation JSON
orderSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Order', orderSchema);
