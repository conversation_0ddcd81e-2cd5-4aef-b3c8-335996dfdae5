const bcrypt = require('bcryptjs');
const Client = require('../models/Client');
const Admin = require('../models/Admin');
const { generateToken, generateRefreshToken } = require('../config/jwt');

class AuthController {
  
  // Inscription client
  async registerClient(req, res) {
    try {
      const { nom, email, telephone, motDePasse, adresse } = req.body;

      // Vérifier si l'email existe déjà
      const existingClient = await Client.findOne({ email });
      if (existingClient) {
        return res.status(400).json({ 
          message: 'Un compte avec cet email existe déjà' 
        });
      }

      // Vérifier si le téléphone existe déjà
      const existingPhone = await Client.findOne({ telephone });
      if (existingPhone) {
        return res.status(400).json({ 
          message: 'Un compte avec ce numéro de téléphone existe déjà' 
        });
      }

      // Créer le nouveau client
      const client = new Client({
        nom,
        email,
        telephone,
        motDePasse,
        adresse
      });

      await client.save();

      // Générer les tokens
      const token = generateToken({ 
        id: client._id, 
        type: 'client' 
      });
      const refreshToken = generateRefreshToken({ 
        id: client._id, 
        type: 'client' 
      });

      res.status(201).json({
        message: 'Compte créé avec succès',
        token,
        refreshToken,
        user: client.toPublicJSON()
      });

    } catch (error) {
      console.error('Erreur inscription client:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la création du compte',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Connexion client
  async loginClient(req, res) {
    try {
      const { email, motDePasse } = req.body;

      // Trouver le client
      const client = await Client.findOne({ email });
      if (!client) {
        return res.status(401).json({ 
          message: 'Email ou mot de passe incorrect' 
        });
      }

      // Vérifier si le compte est actif
      if (!client.isActive) {
        return res.status(401).json({ 
          message: 'Compte désactivé. Contactez l\'administrateur.' 
        });
      }

      // Vérifier le mot de passe
      const isPasswordValid = await client.comparePassword(motDePasse);
      if (!isPasswordValid) {
        return res.status(401).json({ 
          message: 'Email ou mot de passe incorrect' 
        });
      }

      // Mettre à jour la dernière connexion
      await client.updateLastLogin();

      // Générer les tokens
      const token = generateToken({ 
        id: client._id, 
        type: 'client' 
      });
      const refreshToken = generateRefreshToken({ 
        id: client._id, 
        type: 'client' 
      });

      res.json({
        message: 'Connexion réussie',
        token,
        refreshToken,
        user: client.toPublicJSON()
      });

    } catch (error) {
      console.error('Erreur connexion client:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la connexion',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Connexion admin
  async loginAdmin(req, res) {
    try {
      const { email, motDePasse } = req.body;

      // Trouver l'admin
      const admin = await Admin.findOne({ email });
      if (!admin) {
        return res.status(401).json({ 
          message: 'Email ou mot de passe incorrect' 
        });
      }

      // Vérifier si le compte est actif
      if (!admin.isActive) {
        return res.status(401).json({ 
          message: 'Compte désactivé. Contactez le super administrateur.' 
        });
      }

      // Vérifier le mot de passe
      const isPasswordValid = await admin.comparePassword(motDePasse);
      if (!isPasswordValid) {
        return res.status(401).json({ 
          message: 'Email ou mot de passe incorrect' 
        });
      }

      // Mettre à jour la dernière connexion
      await admin.updateLastLogin();

      // Générer les tokens
      const token = generateToken({ 
        id: admin._id, 
        type: 'admin' 
      });
      const refreshToken = generateRefreshToken({ 
        id: admin._id, 
        type: 'admin' 
      });

      res.json({
        message: 'Connexion admin réussie',
        token,
        refreshToken,
        user: admin.toPublicJSON()
      });

    } catch (error) {
      console.error('Erreur connexion admin:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la connexion',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Mettre à jour le token FCM
  async updateFCMToken(req, res) {
    try {
      const { fcmToken } = req.body;
      const userId = req.user._id;
      const userType = req.userType;

      if (!fcmToken) {
        return res.status(400).json({ 
          message: 'Token FCM requis' 
        });
      }

      let user;
      if (userType === 'client') {
        user = await Client.findByIdAndUpdate(
          userId, 
          { fcmToken }, 
          { new: true }
        ).select('-motDePasse');
      } else if (userType === 'admin') {
        user = await Admin.findByIdAndUpdate(
          userId, 
          { fcmToken }, 
          { new: true }
        ).select('-motDePasse');
      }

      if (!user) {
        return res.status(404).json({ 
          message: 'Utilisateur non trouvé' 
        });
      }

      res.json({
        message: 'Token FCM mis à jour avec succès',
        user: user.toPublicJSON()
      });

    } catch (error) {
      console.error('Erreur mise à jour FCM token:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la mise à jour du token',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir le profil utilisateur actuel
  async getProfile(req, res) {
    try {
      res.json({
        user: req.user.toPublicJSON(),
        userType: req.userType
      });
    } catch (error) {
      console.error('Erreur récupération profil:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la récupération du profil',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Changer le mot de passe
  async changePassword(req, res) {
    try {
      const { motDePasseActuel, nouveauMotDePasse } = req.body;
      const userId = req.user._id;
      const userType = req.userType;

      // Validation basique
      if (!motDePasseActuel || !nouveauMotDePasse) {
        return res.status(400).json({ 
          message: 'Mot de passe actuel et nouveau mot de passe requis' 
        });
      }

      if (nouveauMotDePasse.length < 6) {
        return res.status(400).json({ 
          message: 'Le nouveau mot de passe doit contenir au moins 6 caractères' 
        });
      }

      let user;
      if (userType === 'client') {
        user = await Client.findById(userId);
      } else if (userType === 'admin') {
        user = await Admin.findById(userId);
      }

      if (!user) {
        return res.status(404).json({ 
          message: 'Utilisateur non trouvé' 
        });
      }

      // Vérifier le mot de passe actuel
      const isCurrentPasswordValid = await user.comparePassword(motDePasseActuel);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({ 
          message: 'Mot de passe actuel incorrect' 
        });
      }

      // Mettre à jour le mot de passe
      user.motDePasse = nouveauMotDePasse;
      await user.save();

      res.json({
        message: 'Mot de passe modifié avec succès'
      });

    } catch (error) {
      console.error('Erreur changement mot de passe:', error);
      res.status(500).json({ 
        message: 'Erreur lors du changement de mot de passe',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Déconnexion (côté client, supprime le token FCM)
  async logout(req, res) {
    try {
      const userId = req.user._id;
      const userType = req.userType;

      // Supprimer le token FCM
      if (userType === 'client') {
        await Client.findByIdAndUpdate(userId, { fcmToken: null });
      } else if (userType === 'admin') {
        await Admin.findByIdAndUpdate(userId, { fcmToken: null });
      }

      res.json({
        message: 'Déconnexion réussie'
      });

    } catch (error) {
      console.error('Erreur déconnexion:', error);
      res.status(500).json({ 
        message: 'Erreur lors de la déconnexion',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
}

module.exports = new AuthController();
