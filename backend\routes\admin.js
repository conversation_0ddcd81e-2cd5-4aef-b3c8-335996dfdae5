const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const Product = require('../models/Product');
const Client = require('../models/Client');
const StockHistory = require('../models/StockHistory');
const stockService = require('../services/stockService');
const { validate, validateQuery, schemas } = require('../middleware/validation');
const { authenticateAdmin, requirePermission } = require('../middleware/auth');

// Toutes les routes nécessitent une authentification admin
router.use(authenticateAdmin);

// Tableau de bord
router.get('/dashboard', 
  requirePermission('view_analytics'), 
  adminController.getDashboardStats
);

// Gestion des produits
router.post('/products', 
  requirePermission('manage_products'),
  validate(schemas.product), 
  adminController.createProduct
);

router.get('/products', 
  requirePermission('manage_products'),
  validateQuery(schemas.productSearch),
  async (req, res) => {
    try {
      const {
        q,
        categorie,
        prixMin,
        prixMax,
        page = 1,
        limit = 20,
        sortBy = 'dateAjout',
        sortOrder = 'desc',
        includeInactive = false
      } = req.query;

      // Construction de la requête (inclure les produits inactifs pour l'admin)
      const query = includeInactive ? {} : { isActive: true };

      if (q) {
        query.$text = { $search: q };
      }

      if (categorie) {
        query.categorie = categorie;
      }

      if (prixMin || prixMax) {
        query.prix = {};
        if (prixMin) query.prix.$gte = parseFloat(prixMin);
        if (prixMax) query.prix.$lte = parseFloat(prixMax);
      }

      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        Product.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('ajouteParAdmin', 'nom email'),
        Product.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });

    } catch (error) {
      console.error('Erreur récupération produits admin:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des produits',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.get('/products/:id', 
  requirePermission('manage_products'),
  async (req, res) => {
    try {
      const product = await Product.findById(req.params.id)
        .populate('ajouteParAdmin', 'nom email');

      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      res.json(product);
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération du produit',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.put('/products/:id', 
  requirePermission('manage_products'),
  validate(schemas.product), 
  adminController.updateProduct
);

router.delete('/products/:id', 
  requirePermission('manage_products'),
  adminController.deleteProduct
);

// Gestion du stock
router.get('/stock/low', 
  requirePermission('manage_stock'),
  async (req, res) => {
    try {
      const produitsStockFaible = await stockService.obtenirProduitsStockFaible();
      res.json(produitsStockFaible);
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération des produits à stock faible',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.get('/stock/stats', 
  requirePermission('manage_stock'),
  async (req, res) => {
    try {
      const stats = await stockService.obtenirStatistiquesStock();
      res.json(stats);
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération des statistiques de stock',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.put('/stock/:productId', 
  requirePermission('manage_stock'),
  validate(schemas.stockUpdate),
  async (req, res) => {
    try {
      const { productId } = req.params;
      const { quantite, typeOperation, raison, reference } = req.body;

      const result = await stockService.mettreAJourStockManuel(
        productId,
        quantite,
        typeOperation,
        raison,
        req.user._id,
        reference
      );

      res.json(result);
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du stock',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.get('/stock/:productId/history', 
  requirePermission('manage_stock'),
  async (req, res) => {
    try {
      const { productId } = req.params;
      const { page = 1, limit = 20, typeOperation, dateDebut, dateFin } = req.query;

      const options = {
        limit: parseInt(limit),
        skip: (page - 1) * limit,
        typeOperation,
        dateDebut,
        dateFin
      };

      const historique = await StockHistory.obtenirHistoriqueProduit(productId, options);
      const total = await StockHistory.countDocuments({ produitId: productId });

      res.json({
        historique,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      });
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération de l\'historique',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

// Gestion des commandes
router.get('/orders', 
  requirePermission('manage_orders'),
  adminController.getAllOrders
);

router.get('/orders/:id', 
  requirePermission('manage_orders'),
  async (req, res) => {
    try {
      const order = await Order.findById(req.params.id)
        .populate('clientId', 'nom email telephone adresse')
        .populate('produits.produitId', 'nom image categorie')
        .populate('historiqueStatut.modifiePar', 'nom');

      if (!order) {
        return res.status(404).json({
          message: 'Commande non trouvée'
        });
      }

      res.json(order);
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération de la commande',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

router.put('/orders/:id/status', 
  requirePermission('manage_orders'),
  validate(schemas.orderStatus),
  adminController.updateOrderStatus
);

// Gestion des clients
router.get('/clients', 
  requirePermission('manage_clients'),
  async (req, res) => {
    try {
      const { page = 1, limit = 20, search, isActive } = req.query;
      const skip = (page - 1) * limit;

      const query = {};
      if (search) {
        query.$or = [
          { nom: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { telephone: { $regex: search, $options: 'i' } }
        ];
      }
      if (isActive !== undefined) {
        query.isActive = isActive === 'true';
      }

      const [clients, total] = await Promise.all([
        Client.find(query)
          .select('-motDePasse')
          .sort({ dateInscription: -1 })
          .skip(skip)
          .limit(parseInt(limit)),
        Client.countDocuments(query)
      ]);

      res.json({
        clients,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      });
    } catch (error) {
      res.status(500).json({
        message: 'Erreur lors de la récupération des clients',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

module.exports = router;
