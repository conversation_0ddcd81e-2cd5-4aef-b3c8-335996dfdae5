const Joi = require('joi');

// Schémas de validation
const schemas = {
  // Validation pour l'inscription client
  clientRegister: Joi.object({
    nom: Joi.string().trim().min(2).max(100).required()
      .messages({
        'string.empty': 'Le nom est requis',
        'string.min': 'Le nom doit contenir au moins 2 caractères',
        'string.max': 'Le nom ne peut pas dépasser 100 caractères'
      }),
    email: Joi.string().email().lowercase().trim().required()
      .messages({
        'string.email': 'Email invalide',
        'string.empty': 'L\'email est requis'
      }),
    telephone: Joi.string().pattern(/^[0-9+\-\s()]+$/).required()
      .messages({
        'string.pattern.base': 'Numéro de téléphone invalide',
        'string.empty': 'Le téléphone est requis'
      }),
    motDePasse: Joi.string().min(6).required()
      .messages({
        'string.min': 'Le mot de passe doit contenir au moins 6 caractères',
        'string.empty': 'Le mot de passe est requis'
      }),
    adresse: Joi.object({
      rue: Joi.string().trim().required(),
      ville: Joi.string().trim().required(),
      codePostal: Joi.string().trim().required(),
      pays: Joi.string().trim().default('France')
    }).required()
  }),

  // Validation pour la connexion
  login: Joi.object({
    email: Joi.string().email().required(),
    motDePasse: Joi.string().required()
  }),

  // Validation pour les produits
  product: Joi.object({
    nom: Joi.string().trim().min(2).max(200).required(),
    description: Joi.string().trim().min(10).max(1000).required(),
    prix: Joi.number().positive().required(),
    categorie: Joi.string().valid(
      'fruits', 'legumes', 'produits_laitiers', 'viandes', 
      'poissons', 'cereales', 'epices', 'boissons', 
      'conserves', 'surgeles', 'autres'
    ).required(),
    stock: Joi.number().min(0).required(),
    seuilAlerte: Joi.number().min(0).default(10),
    unite: Joi.string().valid('kg', 'g', 'l', 'ml', 'piece', 'boite', 'paquet').required(),
    image: Joi.string().uri().pattern(/\.(jpg|jpeg|png|gif|webp)$/i).required(),
    images: Joi.array().items(Joi.string().uri().pattern(/\.(jpg|jpeg|png|gif|webp)$/i)),
    specifications: Joi.object({
      origine: Joi.string().trim(),
      marque: Joi.string().trim(),
      dateExpiration: Joi.date(),
      conditions_stockage: Joi.string().trim()
    }),
    promotion: Joi.object({
      isActive: Joi.boolean().default(false),
      pourcentage: Joi.number().min(0).max(100),
      dateDebut: Joi.date(),
      dateFin: Joi.date().greater(Joi.ref('dateDebut'))
    })
  }),

  // Validation pour les commandes
  order: Joi.object({
    produits: Joi.array().items(
      Joi.object({
        produitId: Joi.string().hex().length(24).required(),
        quantite: Joi.number().positive().required()
      })
    ).min(1).required(),
    adresseLivraison: Joi.object({
      nom: Joi.string().trim().required(),
      telephone: Joi.string().pattern(/^[0-9+\-\s()]+$/).required(),
      rue: Joi.string().trim().required(),
      ville: Joi.string().trim().required(),
      codePostal: Joi.string().trim().required(),
      pays: Joi.string().trim().default('France'),
      instructions: Joi.string().trim().max(500)
    }).required(),
    notes: Joi.string().trim().max(500)
  }),

  // Validation pour la mise à jour du statut de commande
  orderStatus: Joi.object({
    statut: Joi.string().valid(
      'en_attente', 'confirmee', 'en_preparation', 
      'en_livraison', 'livree', 'annulee', 'retournee'
    ).required(),
    commentaire: Joi.string().trim().max(500),
    dateLivraisonPrevue: Joi.date().min('now')
  }),

  // Validation pour la mise à jour du stock
  stockUpdate: Joi.object({
    quantite: Joi.number().required(),
    typeOperation: Joi.string().valid(
      'ajout_manuel', 'correction_inventaire', 'perte', 'expiration'
    ).required(),
    raison: Joi.string().trim().min(5).max(500).required(),
    reference: Joi.string().trim()
  }),

  // Validation pour la mise à jour du profil client
  clientProfile: Joi.object({
    nom: Joi.string().trim().min(2).max(100),
    telephone: Joi.string().pattern(/^[0-9+\-\s()]+$/),
    adresse: Joi.object({
      rue: Joi.string().trim(),
      ville: Joi.string().trim(),
      codePostal: Joi.string().trim(),
      pays: Joi.string().trim()
    })
  }),

  // Validation pour la recherche et filtres
  productSearch: Joi.object({
    q: Joi.string().trim().min(1).max(100),
    categorie: Joi.string().valid(
      'fruits', 'legumes', 'produits_laitiers', 'viandes', 
      'poissons', 'cereales', 'epices', 'boissons', 
      'conserves', 'surgeles', 'autres'
    ),
    prixMin: Joi.number().min(0),
    prixMax: Joi.number().min(Joi.ref('prixMin')),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(10),
    sortBy: Joi.string().valid('nom', 'prix', 'dateAjout', 'stock'),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  })
};

// Middleware de validation générique
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        message: 'Données invalides',
        errors
      });
    }

    req.body = value;
    next();
  };
};

// Middleware de validation pour les paramètres de requête
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        message: 'Paramètres de requête invalides',
        errors
      });
    }

    req.query = value;
    next();
  };
};

// Middleware de validation pour les paramètres d'URL
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        message: 'Paramètres d\'URL invalides',
        errors
      });
    }

    req.params = value;
    next();
  };
};

module.exports = {
  schemas,
  validate,
  validateQuery,
  validateParams
};
