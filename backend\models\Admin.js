const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const adminSchema = new mongoose.Schema({
  nom: {
    type: String,
    required: [true, 'Le nom est requis'],
    trim: true,
    maxlength: [100, 'Le nom ne peut pas dépasser 100 caractères']
  },
  email: {
    type: String,
    required: [true, 'L\'email est requis'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email invalide']
  },
  motDePasse: {
    type: String,
    required: [true, 'Le mot de passe est requis'],
    minlength: [8, 'Le mot de passe doit contenir au moins 8 caractères']
  },
  role: {
    type: String,
    enum: ['admin', 'super_admin'],
    default: 'admin'
  },
  permissions: [{
    type: String,
    enum: [
      'manage_products',
      'manage_orders', 
      'manage_clients',
      'manage_stock',
      'view_analytics',
      'manage_admins'
    ]
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  fcmToken: {
    type: String,
    default: null
  },
  dateCreation: {
    type: Date,
    default: Date.now
  },
  derniereConnexion: {
    type: Date,
    default: Date.now
  },
  creeParAdmin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    default: null
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
adminSchema.index({ email: 1 });
adminSchema.index({ role: 1 });
adminSchema.index({ isActive: 1 });

// Middleware pour hasher le mot de passe avant sauvegarde
adminSchema.pre('save', async function(next) {
  if (!this.isModified('motDePasse')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.motDePasse = await bcrypt.hash(this.motDePasse, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Méthode pour comparer les mots de passe
adminSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.motDePasse);
};

// Méthode pour obtenir les informations publiques de l'admin
adminSchema.methods.toPublicJSON = function() {
  const admin = this.toObject();
  delete admin.motDePasse;
  delete admin.fcmToken;
  return admin;
};

// Méthode pour vérifier les permissions
adminSchema.methods.hasPermission = function(permission) {
  return this.role === 'super_admin' || this.permissions.includes(permission);
};

// Méthode pour mettre à jour la dernière connexion
adminSchema.methods.updateLastLogin = function() {
  this.derniereConnexion = new Date();
  return this.save();
};

// Définir les permissions par défaut selon le rôle
adminSchema.pre('save', function(next) {
  if (this.isNew && this.role === 'admin' && this.permissions.length === 0) {
    this.permissions = [
      'manage_products',
      'manage_orders',
      'manage_clients',
      'manage_stock',
      'view_analytics'
    ];
  }
  next();
});

module.exports = mongoose.model('Admin', adminSchema);
