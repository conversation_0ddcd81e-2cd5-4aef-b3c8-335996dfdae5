const mongoose = require('mongoose');

const stockHistorySchema = new mongoose.Schema({
  produitId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'L\'ID du produit est requis']
  },
  nomProduit: {
    type: String,
    required: true
  },
  typeOperation: {
    type: String,
    enum: [
      'ajout_manuel',
      'reduction_commande',
      'ajout_livraison',
      'correction_inventaire',
      'retour_client',
      'perte',
      'expiration'
    ],
    required: true
  },
  quantiteAvant: {
    type: Number,
    required: true,
    min: 0
  },
  quantiteApres: {
    type: Number,
    required: true,
    min: 0
  },
  quantiteModifiee: {
    type: Number,
    required: true
  },
  raison: {
    type: String,
    required: [true, 'La raison de la modification est requise'],
    maxlength: [500, 'La raison ne peut pas dépasser 500 caractères']
  },
  reference: {
    type: String, // Peut être un numéro de commande, de livraison, etc.
    trim: true
  },
  modifiePar: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: function() {
      return ['ajout_manuel', 'correction_inventaire', 'perte', 'expiration'].includes(this.typeOperation);
    }
  },
  commandeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: function() {
      return ['reduction_commande', 'retour_client'].includes(this.typeOperation);
    }
  },
  date: {
    type: Date,
    default: Date.now
  },
  metadata: {
    adresseIP: String,
    userAgent: String,
    commentaires: String
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
stockHistorySchema.index({ produitId: 1, date: -1 });
stockHistorySchema.index({ typeOperation: 1 });
stockHistorySchema.index({ date: -1 });
stockHistorySchema.index({ modifiePar: 1 });
stockHistorySchema.index({ commandeId: 1 });

// Méthode statique pour enregistrer une modification de stock
stockHistorySchema.statics.enregistrerModification = async function(options) {
  const {
    produitId,
    nomProduit,
    typeOperation,
    quantiteAvant,
    quantiteApres,
    raison,
    reference,
    modifiePar,
    commandeId,
    metadata
  } = options;

  const quantiteModifiee = quantiteApres - quantiteAvant;

  const historique = new this({
    produitId,
    nomProduit,
    typeOperation,
    quantiteAvant,
    quantiteApres,
    quantiteModifiee,
    raison,
    reference,
    modifiePar,
    commandeId,
    metadata
  });

  return await historique.save();
};

// Méthode statique pour obtenir l'historique d'un produit
stockHistorySchema.statics.obtenirHistoriqueProduit = async function(produitId, options = {}) {
  const {
    limit = 50,
    skip = 0,
    dateDebut,
    dateFin,
    typeOperation
  } = options;

  const query = { produitId };

  if (dateDebut || dateFin) {
    query.date = {};
    if (dateDebut) query.date.$gte = new Date(dateDebut);
    if (dateFin) query.date.$lte = new Date(dateFin);
  }

  if (typeOperation) {
    query.typeOperation = typeOperation;
  }

  return await this.find(query)
    .populate('modifiePar', 'nom email')
    .populate('commandeId', 'numeroCommande')
    .sort({ date: -1 })
    .limit(limit)
    .skip(skip);
};

// Méthode statique pour obtenir les statistiques de stock
stockHistorySchema.statics.obtenirStatistiques = async function(options = {}) {
  const {
    dateDebut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours par défaut
    dateFin = new Date()
  } = options;

  const pipeline = [
    {
      $match: {
        date: { $gte: dateDebut, $lte: dateFin }
      }
    },
    {
      $group: {
        _id: '$typeOperation',
        count: { $sum: 1 },
        totalQuantiteModifiee: { $sum: '$quantiteModifiee' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ];

  return await this.aggregate(pipeline);
};

// Virtual pour le type de modification (augmentation/diminution)
stockHistorySchema.virtual('typeModification').get(function() {
  return this.quantiteModifiee > 0 ? 'augmentation' : 'diminution';
});

// Virtual pour la valeur absolue de la quantité modifiée
stockHistorySchema.virtual('quantiteAbsolue').get(function() {
  return Math.abs(this.quantiteModifiee);
});

// Inclure les virtuals dans la sérialisation JSON
stockHistorySchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('StockHistory', stockHistorySchema);
