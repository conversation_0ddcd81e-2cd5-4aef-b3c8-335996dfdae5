{"version": 3, "sources": ["GestureHandler.ts"], "names": ["State", "TouchEventType", "EventTypes", "GestureHandlerOrchestrator", "InteractionManager", "PointerTracker", "MouseB<PERSON>on", "PointerType", "Gesture<PERSON>andler", "constructor", "delegate", "UNDETERMINED", "enabled", "MOUSE", "newState", "oldState", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "resultEvent", "transformEventData", "lastSentState", "invokeNullableMethod", "state", "ACTIVE", "nativeEvent", "undefined", "_delegate", "init", "viewRef", "attachEventManager", "manager", "setOnPointerDown", "onPointerDown", "bind", "setOnPointerAdd", "onPointerAdd", "setOnPointerUp", "onPointerUp", "setOnPointerRemove", "onPointerRemove", "setOnPointerMove", "onPointerMove", "setOnPointerEnter", "onPointerEnter", "setOnPointerLeave", "onPointerLeave", "setOnPointerCancel", "onPointerCancel", "setOnPointerOutOfBounds", "onPointerOutOfBounds", "setOnPointerMoveOver", "onPointerMoveOver", "setOnPointerMoveOut", "onPointerMoveOut", "setOnWheel", "onWheel", "registerListeners", "onCancel", "onReset", "resetProgress", "reset", "tracker", "resetTracker", "moveToState", "sendIfDisabled", "trackedPointersCount", "config", "needsPointerData", "isFinished", "cancelTouches", "instance", "onHandlerStateChange", "onStateChange", "_newState", "_oldState", "begin", "checkHitSlop", "BEGAN", "fail", "onFail", "FAILED", "cancel", "CANCELLED", "activate", "force", "manualActivation", "onActivate", "end", "onEnd", "END", "getShouldResetProgress", "shouldResetProgress", "setShouldResetProgress", "value", "shouldWaitForHandlerFailure", "handler", "shouldRequireToWaitForFailure", "shouldRequireHandlerToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "shouldHandlerBeCancelledBy", "event", "recordHandlerIfNotPresent", "pointerType", "TOUCH", "cancelMouseAndPenGestures", "tryToSendTouchEvent", "tryToSendMoveEvent", "shouldCancelWhenOutside", "_event", "out", "active", "sendEvent", "sendTouchEvent", "touchEvent", "transformTouchEvent", "numberOfPointers", "pointerInside", "isPointerInBounds", "getAbsoluteCoordsAverage", "transformNativeEvent", "handlerTag", "target", "timeStamp", "Date", "now", "rect", "measure<PERSON>iew", "all", "changed", "trackerData", "trackedPointers", "size", "has", "pointerId", "for<PERSON>ach", "element", "key", "id", "getMappedTouchEventId", "push", "x", "abosoluteCoords", "pageX", "y", "pageY", "absoluteX", "absoluteY", "eventType", "CANCEL", "DOWN", "ADDITIONAL_POINTER_DOWN", "UP", "ADDITIONAL_POINTER_UP", "MOVE", "numberOfTouches", "length", "changedTouches", "allTouches", "cancelEvent", "lastCoords", "lastRelativeCoords", "getRelativeCoordsAverage", "updateGestureConfig", "props", "_config", "onEnabledChange", "validateHitSlops", "removeHandlerFromOrchestrator", "checkCustomActivationCriteria", "criterias", "indexOf", "hasCustomActivationCriteria", "hitSlop", "left", "right", "width", "Error", "height", "top", "bottom", "horizontal", "vertical", "getLastAbsoluteCoords", "offsetX", "offsetY", "isButtonInConfig", "mouseButton", "LEFT", "resetConfig", "onDestroy", "destroy", "_handlerTag", "_tracker", "_state", "_shouldCancelWhenOutside", "_enabled", "_pointerType", "_active", "awaiting", "_awaiting", "activationIndex", "_activationIndex", "_shouldResetProgress", "getTrackedPointersID", "trackedPointersIDs", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "Array", "isArray", "index", "entries", "nativeValue", "setValue"], "mappings": ";;AAAA;AACA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAOEC,cAPF,EAQEC,UARF,QASO,eATP;AAWA,OAAOC,0BAAP,MAAuC,qCAAvC;AACA,OAAOC,kBAAP,MAA+B,6BAA/B;AACA,OAAOC,cAAP,MAA+C,yBAA/C;AAEA,SAASC,WAAT,QAA4B,qCAA5B;AACA,SAASC,WAAT,QAA4B,mBAA5B;AAGA,eAAe,MAAeC,cAAf,CAAyD;AAgBtE;AAWOC,EAAAA,WAAW,CAChBC,QADgB,EAEhB;AAAA,2CA5BoC,IA4BpC;;AAAA,oCA1BsBV,KAAK,CAACW,YA0B5B;;AAAA,sDAxBiC,KAwBjC;;AAAA,yDAvBsC,KAuBtC;;AAAA,sCAtBiB,KAsBjB;;AAAA;;AAAA;;AAAA;;AAAA,qCAjBwB;AAAEC,MAAAA,OAAO,EAAE;AAAX,KAiBxB;;AAAA,sCAfiC,IAAIP,cAAJ,EAejC;;AAAA,8CAZyB,CAYzB;;AAAA,uCAVkB,KAUlB;;AAAA,qCATgB,KAShB;;AAAA,kDAP6B,KAO7B;;AAAA,0CANkCE,WAAW,CAACM,KAM9C;;AAAA;;AAAA,uCAmTiB,CAACC,QAAD,EAAkBC,QAAlB,KAA4C;AAC7D,YAAM;AAAEC,QAAAA,qBAAF;AAAyBC,QAAAA;AAAzB,UACJ,KAAKC,QAAL,CAAcC,OADhB;AAGA,YAAMC,WAAwB,GAAG,KAAKC,kBAAL,CAC/BP,QAD+B,EAE/BC,QAF+B,CAAjC,CAJ6D,CAS7D;AACA;AACA;AACA;;AAEA,UAAI,KAAKO,aAAL,KAAuBR,QAA3B,EAAqC;AACnC,aAAKQ,aAAL,GAAqBR,QAArB;AACAS,QAAAA,oBAAoB,CAACN,2BAAD,EAA8BG,WAA9B,CAApB;AACD;;AACD,UAAI,KAAKI,KAAL,KAAexB,KAAK,CAACyB,MAAzB,EAAiC;AAC/BL,QAAAA,WAAW,CAACM,WAAZ,CAAwBX,QAAxB,GAAmCY,SAAnC;AACAJ,QAAAA,oBAAoB,CAACP,qBAAD,EAAwBI,WAAxB,CAApB;AACD;AACF,KAzUC;;AACA,SAAKQ,SAAL,GAAiBlB,QAAjB;AACD,GA/BqE,CAiCtE;AACA;AACA;;;AAEUmB,EAAAA,IAAI,CAACC,OAAD,EAAkBZ,QAAlB,EAAsD;AAClE,SAAKA,QAAL,GAAgBA,QAAhB;AACA,SAAKY,OAAL,GAAeA,OAAf;AAEA,SAAKN,KAAL,GAAaxB,KAAK,CAACW,YAAnB;AAEA,SAAKD,QAAL,CAAcmB,IAAd,CAAmBC,OAAnB,EAA4B,IAA5B;AACD;;AAEMC,EAAAA,kBAAkB,CAACC,OAAD,EAAuC;AAC9DA,IAAAA,OAAO,CAACC,gBAAR,CAAyB,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACI,eAAR,CAAwB,KAAKC,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAAxB;AACAH,IAAAA,OAAO,CAACM,cAAR,CAAuB,KAAKC,WAAL,CAAiBJ,IAAjB,CAAsB,IAAtB,CAAvB;AACAH,IAAAA,OAAO,CAACQ,kBAAR,CAA2B,KAAKC,eAAL,CAAqBN,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACU,gBAAR,CAAyB,KAAKC,aAAL,CAAmBR,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACY,iBAAR,CAA0B,KAAKC,cAAL,CAAoBV,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACc,iBAAR,CAA0B,KAAKC,cAAL,CAAoBZ,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACgB,kBAAR,CAA2B,KAAKC,eAAL,CAAqBd,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACkB,uBAAR,CAAgC,KAAKC,oBAAL,CAA0BhB,IAA1B,CAA+B,IAA/B,CAAhC;AACAH,IAAAA,OAAO,CAACoB,oBAAR,CAA6B,KAAKC,iBAAL,CAAuBlB,IAAvB,CAA4B,IAA5B,CAA7B;AACAH,IAAAA,OAAO,CAACsB,mBAAR,CAA4B,KAAKC,gBAAL,CAAsBpB,IAAtB,CAA2B,IAA3B,CAA5B;AACAH,IAAAA,OAAO,CAACwB,UAAR,CAAmB,KAAKC,OAAL,CAAatB,IAAb,CAAkB,IAAlB,CAAnB;AAEAH,IAAAA,OAAO,CAAC0B,iBAAR;AACD,GA7DqE,CA+DtE;AACA;AACA;;;AAEUC,EAAAA,QAAQ,GAAS,CAAE;;AACnBC,EAAAA,OAAO,GAAS,CAAE;;AAClBC,EAAAA,aAAa,GAAS,CAAE;;AAE3BC,EAAAA,KAAK,GAAS;AACnB,SAAKC,OAAL,CAAaC,YAAb;AACA,SAAKJ,OAAL;AACA,SAAKC,aAAL;AACA,SAAKnD,QAAL,CAAcoD,KAAd;AACA,SAAKtC,KAAL,GAAaxB,KAAK,CAACW,YAAnB;AACD,GA7EqE,CA+EtE;AACA;AACA;;;AAEOsD,EAAAA,WAAW,CAACnD,QAAD,EAAkBoD,cAAlB,EAA4C;AAC5D,QAAI,KAAK1C,KAAL,KAAeV,QAAnB,EAA6B;AAC3B;AACD;;AAED,UAAMC,QAAQ,GAAG,KAAKS,KAAtB;AACA,SAAKA,KAAL,GAAaV,QAAb;;AAEA,QACE,KAAKiD,OAAL,CAAaI,oBAAb,GAAoC,CAApC,IACA,KAAKC,MAAL,CAAYC,gBADZ,IAEA,KAAKC,UAAL,EAHF,EAIE;AACA,WAAKC,aAAL;AACD;;AAEDpE,IAAAA,0BAA0B,CAACqE,QAA3B,CAAoCC,oBAApC,CACE,IADF,EAEE3D,QAFF,EAGEC,QAHF,EAIEmD,cAJF;AAOA,SAAKQ,aAAL,CAAmB5D,QAAnB,EAA6BC,QAA7B;;AAEA,QAAI,CAAC,KAAKH,OAAN,IAAiB,KAAK0D,UAAL,EAArB,EAAwC;AACtC,WAAK9C,KAAL,GAAaxB,KAAK,CAACW,YAAnB;AACD;AACF;;AAES+D,EAAAA,aAAa,CAACC,SAAD,EAAmBC,SAAnB,EAA2C,CAAE;;AAE7DC,EAAAA,KAAK,GAAS;AACnB,QAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;AACxB;AACD;;AAED,QAAI,KAAKtD,KAAL,KAAexB,KAAK,CAACW,YAAzB,EAAuC;AACrC,WAAKsD,WAAL,CAAiBjE,KAAK,CAAC+E,KAAvB;AACD;AACF;AAED;AACF;AACA;;;AACSC,EAAAA,IAAI,CAACd,cAAD,EAAiC;AAC1C,QAAI,KAAK1C,KAAL,KAAexB,KAAK,CAACyB,MAArB,IAA+B,KAAKD,KAAL,KAAexB,KAAK,CAAC+E,KAAxD,EAA+D;AAC7D;AACA;AACA,WAAKrE,QAAL,CAAcuE,MAAd;AAEA,WAAKhB,WAAL,CAAiBjE,KAAK,CAACkF,MAAvB,EAA+BhB,cAA/B;AACD;;AAED,SAAKL,aAAL;AACD;AAED;AACF;AACA;;;AACSsB,EAAAA,MAAM,CAACjB,cAAD,EAAiC;AAC5C,QACE,KAAK1C,KAAL,KAAexB,KAAK,CAACyB,MAArB,IACA,KAAKD,KAAL,KAAexB,KAAK,CAACW,YADrB,IAEA,KAAKa,KAAL,KAAexB,KAAK,CAAC+E,KAHvB,EAIE;AACA,WAAKpB,QAAL,GADA,CAGA;;AACA,WAAKjD,QAAL,CAAciD,QAAd;AAEA,WAAKM,WAAL,CAAiBjE,KAAK,CAACoF,SAAvB,EAAkClB,cAAlC;AACD;AACF;;AAEMmB,EAAAA,QAAQ,CAACC,KAAK,GAAG,KAAT,EAAgB;AAC7B,QACE,CAAC,KAAKlB,MAAL,CAAYmB,gBAAZ,KAAiC,IAAjC,IAAyCD,KAA1C,MACC,KAAK9D,KAAL,KAAexB,KAAK,CAACW,YAArB,IAAqC,KAAKa,KAAL,KAAexB,KAAK,CAAC+E,KAD3D,CADF,EAGE;AACA,WAAKrE,QAAL,CAAc8E,UAAd;AACA,WAAKvB,WAAL,CAAiBjE,KAAK,CAACyB,MAAvB;AACD;AACF;;AAEMgE,EAAAA,GAAG,GAAG;AACX,QAAI,KAAKjE,KAAL,KAAexB,KAAK,CAAC+E,KAArB,IAA8B,KAAKvD,KAAL,KAAexB,KAAK,CAACyB,MAAvD,EAA+D;AAC7D;AACA,WAAKf,QAAL,CAAcgF,KAAd;AAEA,WAAKzB,WAAL,CAAiBjE,KAAK,CAAC2F,GAAvB;AACD;;AAED,SAAK9B,aAAL;AACD,GAjLqE,CAmLtE;AACA;AACA;;;AAEO+B,EAAAA,sBAAsB,GAAY;AACvC,WAAO,KAAKC,mBAAZ;AACD;;AACMC,EAAAA,sBAAsB,CAACC,KAAD,EAAuB;AAClD,SAAKF,mBAAL,GAA2BE,KAA3B;AACD;;AAEMC,EAAAA,2BAA2B,CAACC,OAAD,EAAoC;AACpE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAO7F,kBAAkB,CAACoE,QAAnB,CAA4BwB,2BAA5B,CACL,IADK,EAELC,OAFK,CAAP;AAID;;AAEMC,EAAAA,6BAA6B,CAACD,OAAD,EAAoC;AACtE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAO7F,kBAAkB,CAACoE,QAAnB,CAA4B2B,oCAA5B,CACL,IADK,EAELF,OAFK,CAAP;AAID;;AAEMG,EAAAA,6BAA6B,CAACH,OAAD,EAAoC;AACtE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,IAAP;AACD;;AAED,WAAO7F,kBAAkB,CAACoE,QAAnB,CAA4B4B,6BAA5B,CACL,IADK,EAELH,OAFK,CAAP;AAID;;AAEMI,EAAAA,wBAAwB,CAACJ,OAAD,EAAoC;AACjE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAO7F,kBAAkB,CAACoE,QAAnB,CAA4B8B,0BAA5B,CACL,IADK,EAELL,OAFK,CAAP;AAID,GAxOqE,CA0OtE;AACA;AACA;;;AAEU/D,EAAAA,aAAa,CAACqE,KAAD,EAA4B;AACjDpG,IAAAA,0BAA0B,CAACqE,QAA3B,CAAoCgC,yBAApC,CAA8D,IAA9D;AACA,SAAKC,WAAL,GAAmBF,KAAK,CAACE,WAAzB;;AAEA,QAAI,KAAKA,WAAL,KAAqBlG,WAAW,CAACmG,KAArC,EAA4C;AAC1CvG,MAAAA,0BAA0B,CAACqE,QAA3B,CAAoCmC,yBAApC,CAA8D,IAA9D;AACD,KANgD,CAQjD;;AACD,GAvPqE,CAwPtE;;;AACUtE,EAAAA,YAAY,CAACkE,KAAD,EAA4B;AAChD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACShE,EAAAA,WAAW,CAACgE,KAAD,EAA4B;AAC/C,SAAKK,mBAAL,CAAyBL,KAAzB;AACD,GA9PqE,CA+PtE;;;AACU9D,EAAAA,eAAe,CAAC8D,KAAD,EAA4B;AACnD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACS5D,EAAAA,aAAa,CAAC4D,KAAD,EAA4B;AACjD,SAAKM,kBAAL,CAAwB,KAAxB,EAA+BN,KAA/B;AACD;;AACSxD,EAAAA,cAAc,CAACwD,KAAD,EAA4B;AAClD,QAAI,KAAKO,uBAAT,EAAkC;AAChC,cAAQ,KAAKtF,KAAb;AACE,aAAKxB,KAAK,CAACyB,MAAX;AACE,eAAK0D,MAAL;AACA;;AACF,aAAKnF,KAAK,CAAC+E,KAAX;AACE,eAAKC,IAAL;AACA;AANJ;;AAQA;AACD;;AAED,SAAK4B,mBAAL,CAAyBL,KAAzB;AACD;;AACS1D,EAAAA,cAAc,CAAC0D,KAAD,EAA4B;AAClD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACStD,EAAAA,eAAe,CAACsD,KAAD,EAA4B;AACnD,SAAKK,mBAAL,CAAyBL,KAAzB;AAEA,SAAKpB,MAAL;AACA,SAAKrB,KAAL;AACD;;AACSX,EAAAA,oBAAoB,CAACoD,KAAD,EAA4B;AACxD,SAAKM,kBAAL,CAAwB,IAAxB,EAA8BN,KAA9B;AACD;;AACSlD,EAAAA,iBAAiB,CAAC0D,MAAD,EAA6B,CACtD;AACD;;AACSxD,EAAAA,gBAAgB,CAACwD,MAAD,EAA6B,CACrD;AACD;;AACStD,EAAAA,OAAO,CAACsD,MAAD,EAA6B,CAC5C;AACD;;AACSF,EAAAA,kBAAkB,CAACG,GAAD,EAAeT,KAAf,EAA0C;AACpE,QAAKS,GAAG,IAAI,KAAKF,uBAAb,IAAyC,CAAC,KAAKlG,OAAnD,EAA4D;AAC1D;AACD;;AAED,QAAI,KAAKqG,MAAT,EAAiB;AACf,WAAKC,SAAL,CAAe,KAAK1F,KAApB,EAA2B,KAAKA,KAAhC;AACD;;AAED,SAAKoF,mBAAL,CAAyBL,KAAzB;AACD;;AAESK,EAAAA,mBAAmB,CAACL,KAAD,EAA4B;AACvD,QAAI,KAAKnC,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK8C,cAAL,CAAoBZ,KAApB;AACD;AACF;;AAEMY,EAAAA,cAAc,CAACZ,KAAD,EAA4B;AAC/C,QAAI,CAAC,KAAK3F,OAAV,EAAmB;AACjB;AACD;;AAED,UAAM;AAAEI,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGA,UAAMiG,UAAwC,GAC5C,KAAKC,mBAAL,CAAyBd,KAAzB,CADF;;AAGA,QAAIa,UAAJ,EAAgB;AACd7F,MAAAA,oBAAoB,CAACP,qBAAD,EAAwBoG,UAAxB,CAApB;AACD;AACF,GA1UqE,CA4UtE;AACA;AACA;;;AA0BQ/F,EAAAA,kBAAkB,CAACP,QAAD,EAAkBC,QAAlB,EAAgD;AACxE,WAAO;AACLW,MAAAA,WAAW,EAAE;AACX4F,QAAAA,gBAAgB,EAAE,KAAKvD,OAAL,CAAaI,oBADpB;AAEX3C,QAAAA,KAAK,EAAEV,QAFI;AAGXyG,QAAAA,aAAa,EAAE,KAAK7G,QAAL,CAAc8G,iBAAd,CACb,KAAKzD,OAAL,CAAa0D,wBAAb,EADa,CAHJ;AAMX,WAAG,KAAKC,oBAAL,EANQ;AAOXC,QAAAA,UAAU,EAAE,KAAKA,UAPN;AAQXC,QAAAA,MAAM,EAAE,KAAK9F,OARF;AASXf,QAAAA,QAAQ,EAAED,QAAQ,KAAKC,QAAb,GAAwBA,QAAxB,GAAmCY,SATlC;AAUX8E,QAAAA,WAAW,EAAE,KAAKA;AAVP,OADR;AAaLoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAbN,KAAP;AAeD;;AAEOV,EAAAA,mBAAmB,CACzBd,KADyB,EAEK;AAC9B,UAAMyB,IAAI,GAAG,KAAKtH,QAAL,CAAcuH,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAKrE,OAAL,CAAasE,eAAjC,CAN8B,CAQ9B;AACA;AACA;AACA;;AACA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAArB,IAA0B,CAACF,WAAW,CAACG,GAAZ,CAAgBhC,KAAK,CAACiC,SAAtB,CAA/B,EAAiE;AAC/D;AACD;;AAEDJ,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAK7E,OAAL,CAAa8E,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAF7B;AAGPC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAH7B;AAIPC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJ5B;AAKPM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AAL5B,OAAT;AAOD,KAVD,EAhB8B,CA4B9B;AACA;;AACA,QAAI3C,KAAK,CAAC+C,SAAN,KAAoBpJ,UAAU,CAACqJ,MAAnC,EAA2C;AACzCpB,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAE,KAAK7E,OAAL,CAAa8E,qBAAb,CAAmCtC,KAAK,CAACiC,SAAzC,CADO;AAEXO,QAAAA,CAAC,EAAExC,KAAK,CAACwC,CAAN,GAAUf,IAAI,CAACiB,KAFP;AAGXC,QAAAA,CAAC,EAAE3C,KAAK,CAAC2C,CAAN,GAAUlB,IAAI,CAACmB,KAHP;AAIXC,QAAAA,SAAS,EAAE7C,KAAK,CAACwC,CAJN;AAKXM,QAAAA,SAAS,EAAE9C,KAAK,CAAC2C;AALN,OAAb;AAOD,KARD,MAQO;AACLd,MAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,cAAMC,EAAU,GAAG,KAAK7E,OAAL,CAAa8E,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAR,QAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,UAAAA,EAAE,EAAEA,EADO;AAEXG,UAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAFzB;AAGXC,UAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAHzB;AAIXC,UAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJxB;AAKXM,UAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AALxB,SAAb;AAOD,OAVD;AAWD;;AAED,QAAII,SAAyB,GAAGrJ,cAAc,CAACU,YAA/C;;AAEA,YAAQ4F,KAAK,CAAC+C,SAAd;AACE,WAAKpJ,UAAU,CAACsJ,IAAhB;AACA,WAAKtJ,UAAU,CAACuJ,uBAAhB;AACEH,QAAAA,SAAS,GAAGrJ,cAAc,CAACuJ,IAA3B;AACA;;AACF,WAAKtJ,UAAU,CAACwJ,EAAhB;AACA,WAAKxJ,UAAU,CAACyJ,qBAAhB;AACEL,QAAAA,SAAS,GAAGrJ,cAAc,CAACyJ,EAA3B;AACA;;AACF,WAAKxJ,UAAU,CAAC0J,IAAhB;AACEN,QAAAA,SAAS,GAAGrJ,cAAc,CAAC2J,IAA3B;AACA;;AACF,WAAK1J,UAAU,CAACqJ,MAAhB;AACED,QAAAA,SAAS,GAAGrJ,cAAc,CAACmF,SAA3B;AACA;AAdJ,KAtD8B,CAuE9B;AACA;AACA;;;AACA,QAAIyE,eAAuB,GAAG3B,GAAG,CAAC4B,MAAlC;;AAEA,QACEvD,KAAK,CAAC+C,SAAN,KAAoBpJ,UAAU,CAACwJ,EAA/B,IACAnD,KAAK,CAAC+C,SAAN,KAAoBpJ,UAAU,CAACyJ,qBAFjC,EAGE;AACA,QAAEE,eAAF;AACD;;AAED,WAAO;AACLnI,MAAAA,WAAW,EAAE;AACXiG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXnG,QAAAA,KAAK,EAAE,KAAKA,KAFD;AAGX8H,QAAAA,SAAS,EAAEA,SAHA;AAIXS,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX2B,QAAAA,eAAe,EAAEA,eANN;AAOXpD,QAAAA,WAAW,EAAE,KAAKA;AAPP,OADR;AAULoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAVN,KAAP;AAYD;;AAEOxD,EAAAA,aAAa,GAAS;AAC5B,UAAMyD,IAAI,GAAG,KAAKtH,QAAL,CAAcuH,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAKrE,OAAL,CAAasE,eAAjC;;AAEA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;AAC1B;AACD;;AAEDF,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAK7E,OAAL,CAAa8E,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAF7B;AAGPC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAH7B;AAIPC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJ5B;AAKPM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AAL5B,OAAT;AAQAf,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAEA,EADO;AAEXG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAFzB;AAGXC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAHzB;AAIXC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJxB;AAKXM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AALxB,OAAb;AAOD,KAlBD;AAoBA,UAAMe,WAA6B,GAAG;AACpCvI,MAAAA,WAAW,EAAE;AACXiG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXnG,QAAAA,KAAK,EAAE,KAAKA,KAFD;AAGX8H,QAAAA,SAAS,EAAErJ,cAAc,CAACmF,SAHf;AAIX2E,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX2B,QAAAA,eAAe,EAAE3B,GAAG,CAAC4B,MANV;AAOXrD,QAAAA,WAAW,EAAE,KAAKA;AAPP,OADuB;AAUpCoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAVyB,KAAtC;AAaA,UAAM;AAAE/G,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGAI,IAAAA,oBAAoB,CAACP,qBAAD,EAAwBiJ,WAAxB,CAApB;AACD;;AAESvC,EAAAA,oBAAoB,GAA4B;AACxD;AACA,UAAMwC,UAAU,GAAG,KAAKnG,OAAL,CAAa0D,wBAAb,EAAnB;AACA,UAAM0C,kBAAkB,GAAG,KAAKpG,OAAL,CAAaqG,wBAAb,EAA3B;AAEA,WAAO;AACLrB,MAAAA,CAAC,EAAEoB,kBAAkB,CAACpB,CADjB;AAELG,MAAAA,CAAC,EAAEiB,kBAAkB,CAACjB,CAFjB;AAGLE,MAAAA,SAAS,EAAEc,UAAU,CAACnB,CAHjB;AAILM,MAAAA,SAAS,EAAEa,UAAU,CAAChB;AAJjB,KAAP;AAMD,GA3hBqE,CA6hBtE;AACA;AACA;;;AAEOmB,EAAAA,mBAAmB,CAAC;AAAEzJ,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAG0J;AAArB,GAAD,EAA6C;AACrE,SAAKC,OAAL,GAAe;AAAE3J,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAG0J;AAAvB,KAAf;AACA,SAAK1J,OAAL,GAAeA,OAAf;AAEA,SAAKF,QAAL,CAAc8J,eAAd,CAA8B5J,OAA9B;;AAEA,QAAI,KAAKwD,MAAL,CAAY0C,uBAAZ,KAAwCnF,SAA5C,EAAuD;AACrD,WAAKmF,uBAAL,GAA+B,KAAK1C,MAAL,CAAY0C,uBAA3C;AACD;;AAED,SAAK2D,gBAAL;;AAEA,QAAI,KAAK7J,OAAT,EAAkB;AAChB;AACD;;AAED,YAAQ,KAAKY,KAAb;AACE,WAAKxB,KAAK,CAACyB,MAAX;AACE,aAAKuD,IAAL,CAAU,IAAV;AACA;;AACF,WAAKhF,KAAK,CAACW,YAAX;AACER,QAAAA,0BAA0B,CAACqE,QAA3B,CAAoCkG,6BAApC,CAAkE,IAAlE;AACA;;AACF;AACE,aAAKvF,MAAL,CAAY,IAAZ;AACA;AATJ;AAWD;;AAESwF,EAAAA,6BAA6B,CAACC,SAAD,EAA4B;AACjE,SAAK,MAAMjC,GAAX,IAAkB,KAAKvE,MAAvB,EAA+B;AAC7B,UAAIwG,SAAS,CAACC,OAAV,CAAkBlC,GAAlB,KAA0B,CAA9B,EAAiC;AAC/B,aAAKmC,2BAAL,GAAmC,IAAnC;AACD;AACF;AACF;;AAEOL,EAAAA,gBAAgB,GAAS;AAC/B,QAAI,CAAC,KAAKrG,MAAL,CAAY2G,OAAjB,EAA0B;AACxB;AACD;;AAED,QACE,KAAK3G,MAAL,CAAY2G,OAAZ,CAAoBC,IAApB,KAA6BrJ,SAA7B,IACA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBE,KAApB,KAA8BtJ,SAD9B,IAEA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBG,KAApB,KAA8BvJ,SAHhC,EAIE;AACA,YAAM,IAAIwJ,KAAJ,CACJ,qEADI,CAAN;AAGD;;AAED,QACE,KAAK/G,MAAL,CAAY2G,OAAZ,CAAoBG,KAApB,KAA8BvJ,SAA9B,IACA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBC,IAApB,KAA6BrJ,SAD7B,IAEA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBE,KAApB,KAA8BtJ,SAHhC,EAIE;AACA,YAAM,IAAIwJ,KAAJ,CACJ,8EADI,CAAN;AAGD;;AAED,QACE,KAAK/G,MAAL,CAAY2G,OAAZ,CAAoBK,MAApB,KAA+BzJ,SAA/B,IACA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBM,GAApB,KAA4B1J,SAD5B,IAEA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBO,MAApB,KAA+B3J,SAHjC,EAIE;AACA,YAAM,IAAIwJ,KAAJ,CACJ,sEADI,CAAN;AAGD;;AAED,QACE,KAAK/G,MAAL,CAAY2G,OAAZ,CAAoBK,MAApB,KAA+BzJ,SAA/B,IACA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBM,GAApB,KAA4B1J,SAD5B,IAEA,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBO,MAApB,KAA+B3J,SAHjC,EAIE;AACA,YAAM,IAAIwJ,KAAJ,CACJ,+EADI,CAAN;AAGD;AACF;;AAEOrG,EAAAA,YAAY,GAAY;AAC9B,QAAI,CAAC,KAAKV,MAAL,CAAY2G,OAAjB,EAA0B;AACxB,aAAO,IAAP;AACD;;AAED,UAAM;AAAEG,MAAAA,KAAF;AAASE,MAAAA;AAAT,QAAoB,KAAK1K,QAAL,CAAcuH,WAAd,EAA1B;AAEA,QAAI+C,IAAI,GAAG,CAAX;AACA,QAAIK,GAAG,GAAG,CAAV;AACA,QAAIJ,KAAa,GAAGC,KAApB;AACA,QAAII,MAAc,GAAGF,MAArB;;AAEA,QAAI,KAAKhH,MAAL,CAAY2G,OAAZ,CAAoBQ,UAApB,KAAmC5J,SAAvC,EAAkD;AAChDqJ,MAAAA,IAAI,IAAI,KAAK5G,MAAL,CAAY2G,OAAZ,CAAoBQ,UAA5B;AACAN,MAAAA,KAAK,IAAI,KAAK7G,MAAL,CAAY2G,OAAZ,CAAoBQ,UAA7B;AACD;;AAED,QAAI,KAAKnH,MAAL,CAAY2G,OAAZ,CAAoBS,QAApB,KAAiC7J,SAArC,EAAgD;AAC9C0J,MAAAA,GAAG,IAAI,KAAKjH,MAAL,CAAY2G,OAAZ,CAAoBS,QAA3B;AACAF,MAAAA,MAAM,IAAI,KAAKlH,MAAL,CAAY2G,OAAZ,CAAoBS,QAA9B;AACD;;AAED,QAAI,KAAKpH,MAAL,CAAY2G,OAAZ,CAAoBC,IAApB,KAA6BrJ,SAAjC,EAA4C;AAC1CqJ,MAAAA,IAAI,GAAG,CAAC,KAAK5G,MAAL,CAAY2G,OAAZ,CAAoBC,IAA5B;AACD;;AAED,QAAI,KAAK5G,MAAL,CAAY2G,OAAZ,CAAoBE,KAApB,KAA8BtJ,SAAlC,EAA6C;AAC3CsJ,MAAAA,KAAK,GAAGC,KAAK,GAAG,KAAK9G,MAAL,CAAY2G,OAAZ,CAAoBE,KAApC;AACD;;AAED,QAAI,KAAK7G,MAAL,CAAY2G,OAAZ,CAAoBM,GAApB,KAA4B1J,SAAhC,EAA2C;AACzC0J,MAAAA,GAAG,GAAG,CAAC,KAAKjH,MAAL,CAAY2G,OAAZ,CAAoBM,GAA3B;AACD;;AAED,QAAI,KAAKjH,MAAL,CAAY2G,OAAZ,CAAoBO,MAApB,KAA+B3J,SAAnC,EAA8C;AAC5C2J,MAAAA,MAAM,GAAGJ,KAAK,GAAG,KAAK9G,MAAL,CAAY2G,OAAZ,CAAoBO,MAArC;AACD;;AACD,QAAI,KAAKlH,MAAL,CAAY2G,OAAZ,CAAoBG,KAApB,KAA8BvJ,SAAlC,EAA6C;AAC3C,UAAI,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBC,IAApB,KAA6BrJ,SAAjC,EAA4C;AAC1CsJ,QAAAA,KAAK,GAAGD,IAAI,GAAG,KAAK5G,MAAL,CAAY2G,OAAZ,CAAoBG,KAAnC;AACD,OAFD,MAEO,IAAI,KAAK9G,MAAL,CAAY2G,OAAZ,CAAoBE,KAApB,KAA8BtJ,SAAlC,EAA6C;AAClDqJ,QAAAA,IAAI,GAAGC,KAAK,GAAG,KAAK7G,MAAL,CAAY2G,OAAZ,CAAoBG,KAAnC;AACD;AACF;;AAED,QAAI,KAAK9G,MAAL,CAAY2G,OAAZ,CAAoBK,MAApB,KAA+BzJ,SAAnC,EAA8C;AAC5C,UAAI,KAAKyC,MAAL,CAAY2G,OAAZ,CAAoBM,GAApB,KAA4B1J,SAAhC,EAA2C;AACzC2J,QAAAA,MAAM,GAAGD,GAAG,GAAG,KAAKjH,MAAL,CAAY2G,OAAZ,CAAoBK,MAAnC;AACD,OAFD,MAEO,IAAI,KAAKhH,MAAL,CAAY2G,OAAZ,CAAoBO,MAApB,KAA+B3J,SAAnC,EAA8C;AACnD0J,QAAAA,GAAG,GAAGC,MAAM,GAAG,KAAKlH,MAAL,CAAY2G,OAAZ,CAAoBK,MAAnC;AACD;AACF;;AAED,UAAMpD,IAAI,GAAG,KAAKtH,QAAL,CAAcuH,WAAd,EAAb;AACA,UAAM;AAAEc,MAAAA,CAAF;AAAKG,MAAAA;AAAL,QAAW,KAAKnF,OAAL,CAAa0H,qBAAb,EAAjB;AACA,UAAMC,OAAe,GAAG3C,CAAC,GAAGf,IAAI,CAACiB,KAAjC;AACA,UAAM0C,OAAe,GAAGzC,CAAC,GAAGlB,IAAI,CAACmB,KAAjC;AAEA,WACEuC,OAAO,IAAIV,IAAX,IAAmBU,OAAO,IAAIT,KAA9B,IAAuCU,OAAO,IAAIN,GAAlD,IAAyDM,OAAO,IAAIL,MADtE;AAGD;;AAEMM,EAAAA,gBAAgB,CAACC,WAAD,EAAuC;AAC5D,WACE,CAACA,WAAD,IACC,CAAC,KAAKzH,MAAL,CAAYyH,WAAb,IAA4BA,WAAW,KAAKvL,WAAW,CAACwL,IADzD,IAEC,KAAK1H,MAAL,CAAYyH,WAAZ,IAA2BA,WAAW,GAAG,KAAKzH,MAAL,CAAYyH,WAHxD;AAKD;;AAESE,EAAAA,WAAW,GAAS,CAAE;;AAEzBC,EAAAA,SAAS,GAAS;AACvB,SAAKtL,QAAL,CAAcuL,OAAd,CAAsB,KAAK7H,MAA3B;AACD,GA/rBqE,CAisBtE;AACA;AACA;;;AAEqB,MAAVuD,UAAU,GAAG;AACtB,WAAO,KAAKuE,WAAZ;AACD;;AACoB,MAAVvE,UAAU,CAAC5B,KAAD,EAAgB;AACnC,SAAKmG,WAAL,GAAmBnG,KAAnB;AACD;;AAEgB,MAAN3B,MAAM,GAAW;AAC1B,WAAO,KAAKmG,OAAZ;AACD;;AAEkB,MAAR7J,QAAQ,GAAG;AACpB,WAAO,KAAKkB,SAAZ;AACD;;AAEiB,MAAPmC,OAAO,GAAG;AACnB,WAAO,KAAKoI,QAAZ;AACD;;AAEe,MAAL3K,KAAK,GAAU;AACxB,WAAO,KAAK4K,MAAZ;AACD;;AACkB,MAAL5K,KAAK,CAACuE,KAAD,EAAe;AAChC,SAAKqG,MAAL,GAAcrG,KAAd;AACD;;AAEiC,MAAvBe,uBAAuB,GAAG;AACnC,WAAO,KAAKuF,wBAAZ;AACD;;AACoC,MAAvBvF,uBAAuB,CAACf,KAAD,EAAQ;AAC3C,SAAKsG,wBAAL,GAAgCtG,KAAhC;AACD;;AAEiB,MAAPnF,OAAO,GAAG;AACnB,WAAO,KAAK0L,QAAZ;AACD;;AACoB,MAAP1L,OAAO,CAACmF,KAAD,EAAQ;AAC3B,SAAKuG,QAAL,GAAgBvG,KAAhB;AACD;;AAEqB,MAAXU,WAAW,GAAgB;AACpC,WAAO,KAAK8F,YAAZ;AACD;;AACwB,MAAX9F,WAAW,CAACV,KAAD,EAAqB;AAC5C,SAAKwG,YAAL,GAAoBxG,KAApB;AACD;;AAEgB,MAANkB,MAAM,GAAG;AAClB,WAAO,KAAKuF,OAAZ;AACD;;AACmB,MAANvF,MAAM,CAAClB,KAAD,EAAQ;AAC1B,SAAKyG,OAAL,GAAezG,KAAf;AACD;;AAEkB,MAAR0G,QAAQ,GAAG;AACpB,WAAO,KAAKC,SAAZ;AACD;;AACqB,MAARD,QAAQ,CAAC1G,KAAD,EAAQ;AAC5B,SAAK2G,SAAL,GAAiB3G,KAAjB;AACD;;AAEyB,MAAf4G,eAAe,GAAG;AAC3B,WAAO,KAAKC,gBAAZ;AACD;;AAC4B,MAAfD,eAAe,CAAC5G,KAAD,EAAQ;AACnC,SAAK6G,gBAAL,GAAwB7G,KAAxB;AACD;;AAE6B,MAAnBF,mBAAmB,GAAG;AAC/B,WAAO,KAAKgH,oBAAZ;AACD;;AACgC,MAAnBhH,mBAAmB,CAACE,KAAD,EAAQ;AACvC,SAAK8G,oBAAL,GAA4B9G,KAA5B;AACD;;AAEM+G,EAAAA,oBAAoB,GAAa;AACtC,WAAO,KAAK/I,OAAL,CAAagJ,kBAApB;AACD;;AAEOzI,EAAAA,UAAU,GAAY;AAC5B,WACE,KAAK9C,KAAL,KAAexB,KAAK,CAAC2F,GAArB,IACA,KAAKnE,KAAL,KAAexB,KAAK,CAACkF,MADrB,IAEA,KAAK1D,KAAL,KAAexB,KAAK,CAACoF,SAHvB;AAKD;;AA1xBqE;;AA6xBxE,SAAS7D,oBAAT,CACEyL,MADF,EAKEzG,KALF,EAMQ;AACN,MAAI,CAACyG,MAAL,EAAa;AACX;AACD;;AAED,MAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChCA,IAAAA,MAAM,CAACzG,KAAD,CAAN;AACA;AACD;;AAED,MAAI,kBAAkByG,MAAlB,IAA4B,OAAOA,MAAM,CAACC,YAAd,KAA+B,UAA/D,EAA2E;AACzE,UAAMhH,OAAO,GAAG+G,MAAM,CAACC,YAAP,EAAhB;;AACA1L,IAAAA,oBAAoB,CAAC0E,OAAD,EAAUM,KAAV,CAApB;AACA;AACD;;AAED,MAAI,EAAE,kBAAkByG,MAApB,CAAJ,EAAiC;AAC/B;AACD;;AAED,QAAM;AAAEE,IAAAA;AAAF,MAA0CF,MAAM,CAACG,YAAvD;;AACA,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcH,UAAd,CAAL,EAAgC;AAC9B;AACD;;AAED,OAAK,MAAM,CAACI,KAAD,EAAQ,CAAC3E,GAAD,EAAM5C,KAAN,CAAR,CAAX,IAAoCmH,UAAU,CAACK,OAAX,EAApC,EAA0D;AACxD,QAAI,EAAE5E,GAAG,IAAIpC,KAAK,CAAC7E,WAAf,CAAJ,EAAiC;AAC/B;AACD,KAHuD,CAKxD;;;AACA,UAAM8L,WAAW,GAAGjH,KAAK,CAAC7E,WAAN,CAAkBiH,GAAlB,CAApB,CANwD,CAQxD;;AACA,QAAI5C,KAAJ,aAAIA,KAAJ,eAAIA,KAAK,CAAE0H,QAAX,EAAqB;AACnB;AACA;AACA1H,MAAAA,KAAK,CAAC0H,QAAN,CAAeD,WAAf;AACD,KAJD,MAIO;AACL;AACAR,MAAAA,MAAM,CAACG,YAAP,CAAoBD,UAApB,CAA+BI,KAA/B,IAAwC,CAAC3E,GAAD,EAAM6E,WAAN,CAAxC;AACD;AACF;;AAED;AACD", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\nimport { State } from '../../State';\nimport {\n  Config,\n  AdaptedEvent,\n  PropsRef,\n  ResultEvent,\n  PointerData,\n  ResultTouchEvent,\n  TouchEventType,\n  EventTypes,\n} from '../interfaces';\nimport EventManager from '../tools/EventManager';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport InteractionManager from '../tools/InteractionManager';\nimport PointerTracker, { TrackerElement } from '../tools/PointerTracker';\nimport IGestureHandler from './IGestureHandler';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport { PointerType } from '../../PointerType';\nimport { GestureHandlerDelegate } from '../tools/GestureHandlerDelegate';\n\nexport default abstract class GestureHandler implements IGestureHandler {\n  private lastSentState: State | null = null;\n\n  private _state: State = State.UNDETERMINED;\n\n  private _shouldCancelWhenOutside = false;\n  protected hasCustomActivationCriteria = false;\n  private _enabled = false;\n\n  private viewRef!: number;\n  private propsRef!: React.RefObject<unknown>;\n  private _handlerTag!: number;\n  private _config: Config = { enabled: false };\n\n  private _tracker: PointerTracker = new PointerTracker();\n\n  // Orchestrator properties\n  private _activationIndex = 0;\n\n  private _awaiting = false;\n  private _active = false;\n\n  private _shouldResetProgress = false;\n  private _pointerType: PointerType = PointerType.MOUSE;\n\n  private _delegate: GestureHandlerDelegate<unknown, IGestureHandler>;\n\n  public constructor(\n    delegate: GestureHandlerDelegate<unknown, IGestureHandler>\n  ) {\n    this._delegate = delegate;\n  }\n\n  //\n  // Initializing handler\n  //\n\n  protected init(viewRef: number, propsRef: React.RefObject<unknown>) {\n    this.propsRef = propsRef;\n    this.viewRef = viewRef;\n\n    this.state = State.UNDETERMINED;\n\n    this.delegate.init(viewRef, this);\n  }\n\n  public attachEventManager(manager: EventManager<unknown>): void {\n    manager.setOnPointerDown(this.onPointerDown.bind(this));\n    manager.setOnPointerAdd(this.onPointerAdd.bind(this));\n    manager.setOnPointerUp(this.onPointerUp.bind(this));\n    manager.setOnPointerRemove(this.onPointerRemove.bind(this));\n    manager.setOnPointerMove(this.onPointerMove.bind(this));\n    manager.setOnPointerEnter(this.onPointerEnter.bind(this));\n    manager.setOnPointerLeave(this.onPointerLeave.bind(this));\n    manager.setOnPointerCancel(this.onPointerCancel.bind(this));\n    manager.setOnPointerOutOfBounds(this.onPointerOutOfBounds.bind(this));\n    manager.setOnPointerMoveOver(this.onPointerMoveOver.bind(this));\n    manager.setOnPointerMoveOut(this.onPointerMoveOut.bind(this));\n    manager.setOnWheel(this.onWheel.bind(this));\n\n    manager.registerListeners();\n  }\n\n  //\n  // Resetting handler\n  //\n\n  protected onCancel(): void {}\n  protected onReset(): void {}\n  protected resetProgress(): void {}\n\n  public reset(): void {\n    this.tracker.resetTracker();\n    this.onReset();\n    this.resetProgress();\n    this.delegate.reset();\n    this.state = State.UNDETERMINED;\n  }\n\n  //\n  // State logic\n  //\n\n  public moveToState(newState: State, sendIfDisabled?: boolean) {\n    if (this.state === newState) {\n      return;\n    }\n\n    const oldState = this.state;\n    this.state = newState;\n\n    if (\n      this.tracker.trackedPointersCount > 0 &&\n      this.config.needsPointerData &&\n      this.isFinished()\n    ) {\n      this.cancelTouches();\n    }\n\n    GestureHandlerOrchestrator.instance.onHandlerStateChange(\n      this,\n      newState,\n      oldState,\n      sendIfDisabled\n    );\n\n    this.onStateChange(newState, oldState);\n\n    if (!this.enabled && this.isFinished()) {\n      this.state = State.UNDETERMINED;\n    }\n  }\n\n  protected onStateChange(_newState: State, _oldState: State): void {}\n\n  public begin(): void {\n    if (!this.checkHitSlop()) {\n      return;\n    }\n\n    if (this.state === State.UNDETERMINED) {\n      this.moveToState(State.BEGAN);\n    }\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send fail event\n   */\n  public fail(sendIfDisabled?: boolean): void {\n    if (this.state === State.ACTIVE || this.state === State.BEGAN) {\n      // Here the order of calling the delegate and moveToState is important.\n      // At this point we can use currentState as previuos state, because immediately after changing cursor we call moveToState method.\n      this.delegate.onFail();\n\n      this.moveToState(State.FAILED, sendIfDisabled);\n    }\n\n    this.resetProgress();\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send cancel event\n   */\n  public cancel(sendIfDisabled?: boolean): void {\n    if (\n      this.state === State.ACTIVE ||\n      this.state === State.UNDETERMINED ||\n      this.state === State.BEGAN\n    ) {\n      this.onCancel();\n\n      // Same as above - order matters\n      this.delegate.onCancel();\n\n      this.moveToState(State.CANCELLED, sendIfDisabled);\n    }\n  }\n\n  public activate(force = false) {\n    if (\n      (this.config.manualActivation !== true || force) &&\n      (this.state === State.UNDETERMINED || this.state === State.BEGAN)\n    ) {\n      this.delegate.onActivate();\n      this.moveToState(State.ACTIVE);\n    }\n  }\n\n  public end() {\n    if (this.state === State.BEGAN || this.state === State.ACTIVE) {\n      // Same as above - order matters\n      this.delegate.onEnd();\n\n      this.moveToState(State.END);\n    }\n\n    this.resetProgress();\n  }\n\n  //\n  // Methods for orchestrator\n  //\n\n  public getShouldResetProgress(): boolean {\n    return this.shouldResetProgress;\n  }\n  public setShouldResetProgress(value: boolean): void {\n    this.shouldResetProgress = value;\n  }\n\n  public shouldWaitForHandlerFailure(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.instance.shouldWaitForHandlerFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRequireToWaitForFailure(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.instance.shouldRequireHandlerToWaitForFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRecognizeSimultaneously(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return true;\n    }\n\n    return InteractionManager.instance.shouldRecognizeSimultaneously(\n      this,\n      handler\n    );\n  }\n\n  public shouldBeCancelledByOther(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.instance.shouldHandlerBeCancelledBy(\n      this,\n      handler\n    );\n  }\n\n  //\n  // Event actions\n  //\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.instance.recordHandlerIfNotPresent(this);\n    this.pointerType = event.pointerType;\n\n    if (this.pointerType === PointerType.TOUCH) {\n      GestureHandlerOrchestrator.instance.cancelMouseAndPenGestures(this);\n    }\n\n    // TODO: Bring back touch events along with introducing `handleDown` method that will handle handler specific stuff\n  }\n  // Adding another pointer to existing ones\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerUp(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  // Removing pointer, when there is more than one pointers\n  protected onPointerRemove(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(false, event);\n  }\n  protected onPointerLeave(event: AdaptedEvent): void {\n    if (this.shouldCancelWhenOutside) {\n      switch (this.state) {\n        case State.ACTIVE:\n          this.cancel();\n          break;\n        case State.BEGAN:\n          this.fail();\n          break;\n      }\n      return;\n    }\n\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerEnter(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerCancel(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n\n    this.cancel();\n    this.reset();\n  }\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(true, event);\n  }\n  protected onPointerMoveOver(_event: AdaptedEvent): void {\n    // Used only by hover gesture handler atm\n  }\n  protected onPointerMoveOut(_event: AdaptedEvent): void {\n    // Used only by hover gesture handler atm\n  }\n  protected onWheel(_event: AdaptedEvent): void {\n    // Used only by pan gesture handler\n  }\n  protected tryToSendMoveEvent(out: boolean, event: AdaptedEvent): void {\n    if ((out && this.shouldCancelWhenOutside) || !this.enabled) {\n      return;\n    }\n\n    if (this.active) {\n      this.sendEvent(this.state, this.state);\n    }\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected tryToSendTouchEvent(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n\n  public sendTouchEvent(event: AdaptedEvent): void {\n    if (!this.enabled) {\n      return;\n    }\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    const touchEvent: ResultTouchEvent | undefined =\n      this.transformTouchEvent(event);\n\n    if (touchEvent) {\n      invokeNullableMethod(onGestureHandlerEvent, touchEvent);\n    }\n  }\n\n  //\n  // Events Sending\n  //\n\n  public sendEvent = (newState: State, oldState: State): void => {\n    const { onGestureHandlerEvent, onGestureHandlerStateChange }: PropsRef =\n      this.propsRef.current as PropsRef;\n\n    const resultEvent: ResultEvent = this.transformEventData(\n      newState,\n      oldState\n    );\n\n    // In the new API oldState field has to be undefined, unless we send event state changed\n    // Here the order is flipped to avoid workarounds such as making backup of the state and setting it to undefined first, then changing it back\n    // Flipping order with setting oldState to undefined solves issue, when events were being sent twice instead of once\n    // However, this may cause trouble in the future (but for now we don't know that)\n\n    if (this.lastSentState !== newState) {\n      this.lastSentState = newState;\n      invokeNullableMethod(onGestureHandlerStateChange, resultEvent);\n    }\n    if (this.state === State.ACTIVE) {\n      resultEvent.nativeEvent.oldState = undefined;\n      invokeNullableMethod(onGestureHandlerEvent, resultEvent);\n    }\n  };\n\n  private transformEventData(newState: State, oldState: State): ResultEvent {\n    return {\n      nativeEvent: {\n        numberOfPointers: this.tracker.trackedPointersCount,\n        state: newState,\n        pointerInside: this.delegate.isPointerInBounds(\n          this.tracker.getAbsoluteCoordsAverage()\n        ),\n        ...this.transformNativeEvent(),\n        handlerTag: this.handlerTag,\n        target: this.viewRef,\n        oldState: newState !== oldState ? oldState : undefined,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private transformTouchEvent(\n    event: AdaptedEvent\n  ): ResultTouchEvent | undefined {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.trackedPointers;\n\n    // This if handles edge case where all pointers have been cancelled\n    // When pointercancel is triggered, reset method is called. This means that tracker will be reset after first pointer being cancelled\n    // The problem is, that handler will receive another pointercancel event from the rest of the pointers\n    // To avoid crashing, we don't send event if tracker tracks no pointers, i.e. has been reset\n    if (trackerData.size === 0 || !trackerData.has(event.pointerId)) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n    });\n\n    // Each pointer sends its own event, so we want changed touches to contain only the pointer that has changed.\n    // However, if the event is cancel, we want to cancel all pointers to avoid crashes\n    if (event.eventType !== EventTypes.CANCEL) {\n      changed.push({\n        id: this.tracker.getMappedTouchEventId(event.pointerId),\n        x: event.x - rect.pageX,\n        y: event.y - rect.pageY,\n        absoluteX: event.x,\n        absoluteY: event.y,\n      });\n    } else {\n      trackerData.forEach((element: TrackerElement, key: number): void => {\n        const id: number = this.tracker.getMappedTouchEventId(key);\n\n        changed.push({\n          id: id,\n          x: element.abosoluteCoords.x - rect.pageX,\n          y: element.abosoluteCoords.y - rect.pageY,\n          absoluteX: element.abosoluteCoords.x,\n          absoluteY: element.abosoluteCoords.y,\n        });\n      });\n    }\n\n    let eventType: TouchEventType = TouchEventType.UNDETERMINED;\n\n    switch (event.eventType) {\n      case EventTypes.DOWN:\n      case EventTypes.ADDITIONAL_POINTER_DOWN:\n        eventType = TouchEventType.DOWN;\n        break;\n      case EventTypes.UP:\n      case EventTypes.ADDITIONAL_POINTER_UP:\n        eventType = TouchEventType.UP;\n        break;\n      case EventTypes.MOVE:\n        eventType = TouchEventType.MOVE;\n        break;\n      case EventTypes.CANCEL:\n        eventType = TouchEventType.CANCELLED;\n        break;\n    }\n\n    // Here, when we receive up event, we want to decrease number of touches\n    // That's because we want handler to send information that there's one pointer less\n    // However, we still want this pointer to be present in allTouches array, so that its data can be accessed\n    let numberOfTouches: number = all.length;\n\n    if (\n      event.eventType === EventTypes.UP ||\n      event.eventType === EventTypes.ADDITIONAL_POINTER_UP\n    ) {\n      --numberOfTouches;\n    }\n\n    return {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.state,\n        eventType: eventType,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: numberOfTouches,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private cancelTouches(): void {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.trackedPointers;\n\n    if (trackerData.size === 0) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n\n      changed.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n    });\n\n    const cancelEvent: ResultTouchEvent = {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.state,\n        eventType: TouchEventType.CANCELLED,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: all.length,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    invokeNullableMethod(onGestureHandlerEvent, cancelEvent);\n  }\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    // Those properties are shared by most handlers and if not this method will be overriden\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    const lastRelativeCoords = this.tracker.getRelativeCoordsAverage();\n\n    return {\n      x: lastRelativeCoords.x,\n      y: lastRelativeCoords.y,\n      absoluteX: lastCoords.x,\n      absoluteY: lastCoords.y,\n    };\n  }\n\n  //\n  // Handling config\n  //\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    this._config = { enabled: enabled, ...props };\n    this.enabled = enabled;\n\n    this.delegate.onEnabledChange(enabled);\n\n    if (this.config.shouldCancelWhenOutside !== undefined) {\n      this.shouldCancelWhenOutside = this.config.shouldCancelWhenOutside;\n    }\n\n    this.validateHitSlops();\n\n    if (this.enabled) {\n      return;\n    }\n\n    switch (this.state) {\n      case State.ACTIVE:\n        this.fail(true);\n        break;\n      case State.UNDETERMINED:\n        GestureHandlerOrchestrator.instance.removeHandlerFromOrchestrator(this);\n        break;\n      default:\n        this.cancel(true);\n        break;\n    }\n  }\n\n  protected checkCustomActivationCriteria(criterias: string[]): void {\n    for (const key in this.config) {\n      if (criterias.indexOf(key) >= 0) {\n        this.hasCustomActivationCriteria = true;\n      }\n    }\n  }\n\n  private validateHitSlops(): void {\n    if (!this.config.hitSlop) {\n      return;\n    }\n\n    if (\n      this.config.hitSlop.left !== undefined &&\n      this.config.hitSlop.right !== undefined &&\n      this.config.hitSlop.width !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define left, right and width at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.width !== undefined &&\n      this.config.hitSlop.left === undefined &&\n      this.config.hitSlop.right === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When width is defined, either left or right has to be defined'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top !== undefined &&\n      this.config.hitSlop.bottom !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define top, bottom and height at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top === undefined &&\n      this.config.hitSlop.bottom === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When height is defined, either top or bottom has to be defined'\n      );\n    }\n  }\n\n  private checkHitSlop(): boolean {\n    if (!this.config.hitSlop) {\n      return true;\n    }\n\n    const { width, height } = this.delegate.measureView();\n\n    let left = 0;\n    let top = 0;\n    let right: number = width;\n    let bottom: number = height;\n\n    if (this.config.hitSlop.horizontal !== undefined) {\n      left -= this.config.hitSlop.horizontal;\n      right += this.config.hitSlop.horizontal;\n    }\n\n    if (this.config.hitSlop.vertical !== undefined) {\n      top -= this.config.hitSlop.vertical;\n      bottom += this.config.hitSlop.vertical;\n    }\n\n    if (this.config.hitSlop.left !== undefined) {\n      left = -this.config.hitSlop.left;\n    }\n\n    if (this.config.hitSlop.right !== undefined) {\n      right = width + this.config.hitSlop.right;\n    }\n\n    if (this.config.hitSlop.top !== undefined) {\n      top = -this.config.hitSlop.top;\n    }\n\n    if (this.config.hitSlop.bottom !== undefined) {\n      bottom = width + this.config.hitSlop.bottom;\n    }\n    if (this.config.hitSlop.width !== undefined) {\n      if (this.config.hitSlop.left !== undefined) {\n        right = left + this.config.hitSlop.width;\n      } else if (this.config.hitSlop.right !== undefined) {\n        left = right - this.config.hitSlop.width;\n      }\n    }\n\n    if (this.config.hitSlop.height !== undefined) {\n      if (this.config.hitSlop.top !== undefined) {\n        bottom = top + this.config.hitSlop.height;\n      } else if (this.config.hitSlop.bottom !== undefined) {\n        top = bottom - this.config.hitSlop.height;\n      }\n    }\n\n    const rect = this.delegate.measureView();\n    const { x, y } = this.tracker.getLastAbsoluteCoords();\n    const offsetX: number = x - rect.pageX;\n    const offsetY: number = y - rect.pageY;\n\n    return (\n      offsetX >= left && offsetX <= right && offsetY >= top && offsetY <= bottom\n    );\n  }\n\n  public isButtonInConfig(mouseButton: MouseButton | undefined) {\n    return (\n      !mouseButton ||\n      (!this.config.mouseButton && mouseButton === MouseButton.LEFT) ||\n      (this.config.mouseButton && mouseButton & this.config.mouseButton)\n    );\n  }\n\n  protected resetConfig(): void {}\n\n  public onDestroy(): void {\n    this.delegate.destroy(this.config);\n  }\n\n  //\n  // Getters and setters\n  //\n\n  public get handlerTag() {\n    return this._handlerTag;\n  }\n  public set handlerTag(value: number) {\n    this._handlerTag = value;\n  }\n\n  public get config(): Config {\n    return this._config;\n  }\n\n  public get delegate() {\n    return this._delegate;\n  }\n\n  public get tracker() {\n    return this._tracker;\n  }\n\n  public get state(): State {\n    return this._state;\n  }\n  protected set state(value: State) {\n    this._state = value;\n  }\n\n  public get shouldCancelWhenOutside() {\n    return this._shouldCancelWhenOutside;\n  }\n  protected set shouldCancelWhenOutside(value) {\n    this._shouldCancelWhenOutside = value;\n  }\n\n  public get enabled() {\n    return this._enabled;\n  }\n  protected set enabled(value) {\n    this._enabled = value;\n  }\n\n  public get pointerType(): PointerType {\n    return this._pointerType;\n  }\n  protected set pointerType(value: PointerType) {\n    this._pointerType = value;\n  }\n\n  public get active() {\n    return this._active;\n  }\n  protected set active(value) {\n    this._active = value;\n  }\n\n  public get awaiting() {\n    return this._awaiting;\n  }\n  protected set awaiting(value) {\n    this._awaiting = value;\n  }\n\n  public get activationIndex() {\n    return this._activationIndex;\n  }\n  protected set activationIndex(value) {\n    this._activationIndex = value;\n  }\n\n  public get shouldResetProgress() {\n    return this._shouldResetProgress;\n  }\n  protected set shouldResetProgress(value) {\n    this._shouldResetProgress = value;\n  }\n\n  public getTrackedPointersID(): number[] {\n    return this.tracker.trackedPointersIDs;\n  }\n\n  private isFinished(): boolean {\n    return (\n      this.state === State.END ||\n      this.state === State.FAILED ||\n      this.state === State.CANCELLED\n    );\n  }\n}\n\nfunction invokeNullableMethod(\n  method:\n    | ((event: ResultEvent | ResultTouchEvent) => void)\n    | { __getHandler: () => (event: ResultEvent | ResultTouchEvent) => void }\n    | { __nodeConfig: { argMapping: unknown[] } },\n  event: ResultEvent | ResultTouchEvent\n): void {\n  if (!method) {\n    return;\n  }\n\n  if (typeof method === 'function') {\n    method(event);\n    return;\n  }\n\n  if ('__getHandler' in method && typeof method.__getHandler === 'function') {\n    const handler = method.__getHandler();\n    invokeNullableMethod(handler, event);\n    return;\n  }\n\n  if (!('__nodeConfig' in method)) {\n    return;\n  }\n\n  const { argMapping }: { argMapping: unknown } = method.__nodeConfig;\n  if (!Array.isArray(argMapping)) {\n    return;\n  }\n\n  for (const [index, [key, value]] of argMapping.entries()) {\n    if (!(key in event.nativeEvent)) {\n      continue;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    const nativeValue = event.nativeEvent[key];\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (value?.setValue) {\n      // Reanimated API\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call\n      value.setValue(nativeValue);\n    } else {\n      // RN Animated API\n      method.__nodeConfig.argMapping[index] = [key, nativeValue];\n    }\n  }\n\n  return;\n}\n"]}