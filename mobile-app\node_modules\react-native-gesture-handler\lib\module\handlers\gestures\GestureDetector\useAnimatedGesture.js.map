{"version": 3, "sources": ["useAnimatedGesture.ts"], "names": ["CALLBACK_TYPE", "Reanimated", "GestureStateManager", "State", "TouchEventType", "tagMessage", "<PERSON><PERSON><PERSON><PERSON>", "type", "gesture", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "eventType", "UNDEFINED", "runWorklet", "event", "args", "handler", "isWorklet", "console", "warn", "isStateChangeEvent", "oldState", "isTouchEvent", "useAnimatedGesture", "preparedGesture", "needsRebuild", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "value", "i", "length", "handlerTag", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "animatedHandlers"], "mappings": "AAAA,SAA2BA,aAA3B,QAAgD,YAAhD;AACA,SAASC,UAAT,QAA2B,sBAA3B;AAMA,SACEC,mBADF,QAGO,wBAHP;AAIA,SAASC,KAAT,QAAsB,gBAAtB;AACA,SAASC,cAAT,QAA+B,yBAA/B;AACA,SAASC,UAAT,QAA2B,gBAA3B;;AAGA,SAASC,UAAT,CACEC,IADF,EAEEC,OAFF,EAGE;AACA;;AACA,UAAQD,IAAR;AACE,SAAKP,aAAa,CAACS,KAAnB;AACE,aAAOD,OAAO,CAACE,OAAf;;AACF,SAAKV,aAAa,CAACW,KAAnB;AACE,aAAOH,OAAO,CAACI,OAAf;;AACF,SAAKZ,aAAa,CAACa,MAAnB;AACE,aAAOL,OAAO,CAACM,QAAf;;AACF,SAAKd,aAAa,CAACe,MAAnB;AACE,aAAOP,OAAO,CAACQ,QAAf;;AACF,SAAKhB,aAAa,CAACiB,GAAnB;AACE,aAAOT,OAAO,CAACU,KAAf;;AACF,SAAKlB,aAAa,CAACmB,QAAnB;AACE,aAAOX,OAAO,CAACY,UAAf;;AACF,SAAKpB,aAAa,CAACqB,YAAnB;AACE,aAAOb,OAAO,CAACc,aAAf;;AACF,SAAKtB,aAAa,CAACuB,YAAnB;AACE,aAAOf,OAAO,CAACgB,aAAf;;AACF,SAAKxB,aAAa,CAACyB,UAAnB;AACE,aAAOjB,OAAO,CAACkB,WAAf;;AACF,SAAK1B,aAAa,CAAC2B,iBAAnB;AACE,aAAOnB,OAAO,CAACoB,kBAAf;AApBJ;AAsBD;;AAED,SAASC,4BAAT,CACEC,SADF,EAEiB;AACf;;AACA,UAAQA,SAAR;AACE,SAAK1B,cAAc,CAACiB,YAApB;AACE,aAAOrB,aAAa,CAACqB,YAArB;;AACF,SAAKjB,cAAc,CAACmB,YAApB;AACE,aAAOvB,aAAa,CAACuB,YAArB;;AACF,SAAKnB,cAAc,CAACqB,UAApB;AACE,aAAOzB,aAAa,CAACyB,UAArB;;AACF,SAAKrB,cAAc,CAACuB,iBAApB;AACE,aAAO3B,aAAa,CAAC2B,iBAArB;AARJ;;AAUA,SAAO3B,aAAa,CAAC+B,SAArB;AACD;;AAED,SAASC,UAAT,CACEzB,IADF,EAEEC,OAFF,EAGEyB,KAHF,EAIE,GAAGC,IAJL,EAKE;AACA;;AACA,QAAMC,OAAO,GAAG7B,UAAU,CAACC,IAAD,EAAOC,OAAP,CAA1B;;AACA,MAAIA,OAAO,CAAC4B,SAAR,CAAkB7B,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACA4B,IAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGF,KAAH,EAAU,GAAGC,IAAb,CAAP;AACD,GAJD,MAIO,IAAIC,OAAJ,EAAa;AAClBE,IAAAA,OAAO,CAACC,IAAR,CAAajC,UAAU,CAAC,6CAAD,CAAvB;AACD;AACF;;AAED,SAASkC,kBAAT,CACEN,KADF,EAEoC;AAClC,YADkC,CAElC;;AACA,SAAOA,KAAK,CAACO,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACER,KADF,EAE8B;AAC5B;;AACA,SAAOA,KAAK,CAACH,SAAN,IAAmB,IAA1B;AACD;;AAED,OAAO,SAASY,kBAAT,CACLC,eADK,EAELC,YAFK,EAGL;AACA,MAAI,CAAC3C,UAAL,EAAiB;AACf;AACD,GAHD,CAKA;AACA;AACA;;;AACA,QAAM4C,uBAAuB,GAAG5C,UAAU,CAAC6C,cAAX,CAE9B,IAF8B,CAAhC,CARA,CAYA;;AACA,QAAMC,eAAe,GAAG9C,UAAU,CAAC6C,cAAX,CAEtB,EAFsB,CAAxB,CAbA,CAiBA;;AACA,QAAME,gBAA2C,GAAG,EAApD;;AAEA,QAAMC,QAAQ,GACZhB,KADe,IAEZ;AACH;;AAEA,UAAMiB,eAAe,GAAGL,uBAAuB,CAACM,KAAhD;;AACA,QAAI,CAACD,eAAL,EAAsB;AACpB;AACD;;AAED,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,eAAe,CAACG,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC/C,YAAM5C,OAAO,GAAG0C,eAAe,CAACE,CAAD,CAA/B;;AAEA,UAAInB,KAAK,CAACqB,UAAN,KAAqB9C,OAAO,CAAC8C,UAAjC,EAA6C;AAC3C;AACD;;AAED,UAAIf,kBAAkB,CAACN,KAAD,CAAtB,EAA+B;AAC7B,YACEA,KAAK,CAACO,QAAN,KAAmBrC,KAAK,CAACoD,YAAzB,IACAtB,KAAK,CAACuB,KAAN,KAAgBrD,KAAK,CAACM,KAFxB,EAGE;AACAuB,UAAAA,UAAU,CAAChC,aAAa,CAACS,KAAf,EAAsBD,OAAtB,EAA+ByB,KAA/B,CAAV;AACD,SALD,MAKO,IACL,CAACA,KAAK,CAACO,QAAN,KAAmBrC,KAAK,CAACM,KAAzB,IACCwB,KAAK,CAACO,QAAN,KAAmBrC,KAAK,CAACoD,YAD3B,KAEAtB,KAAK,CAACuB,KAAN,KAAgBrD,KAAK,CAACsD,MAHjB,EAIL;AACAzB,UAAAA,UAAU,CAAChC,aAAa,CAACW,KAAf,EAAsBH,OAAtB,EAA+ByB,KAA/B,CAAV;AACAc,UAAAA,eAAe,CAACI,KAAhB,CAAsB3C,OAAO,CAAC8C,UAA9B,IAA4CI,SAA5C;AACD,SAPM,MAOA,IACLzB,KAAK,CAACO,QAAN,KAAmBP,KAAK,CAACuB,KAAzB,IACAvB,KAAK,CAACuB,KAAN,KAAgBrD,KAAK,CAACc,GAFjB,EAGL;AACA,cAAIgB,KAAK,CAACO,QAAN,KAAmBrC,KAAK,CAACsD,MAA7B,EAAqC;AACnCzB,YAAAA,UAAU,CAAChC,aAAa,CAACiB,GAAf,EAAoBT,OAApB,EAA6ByB,KAA7B,EAAoC,IAApC,CAAV;AACD;;AACDD,UAAAA,UAAU,CAAChC,aAAa,CAACmB,QAAf,EAAyBX,OAAzB,EAAkCyB,KAAlC,EAAyC,IAAzC,CAAV;AACD,SARM,MAQA,IACL,CAACA,KAAK,CAACuB,KAAN,KAAgBrD,KAAK,CAACwD,MAAtB,IAAgC1B,KAAK,CAACuB,KAAN,KAAgBrD,KAAK,CAACyD,SAAvD,KACA3B,KAAK,CAACuB,KAAN,KAAgBvB,KAAK,CAACO,QAFjB,EAGL;AACA,cAAIP,KAAK,CAACO,QAAN,KAAmBrC,KAAK,CAACsD,MAA7B,EAAqC;AACnCzB,YAAAA,UAAU,CAAChC,aAAa,CAACiB,GAAf,EAAoBT,OAApB,EAA6ByB,KAA7B,EAAoC,KAApC,CAAV;AACD;;AACDD,UAAAA,UAAU,CAAChC,aAAa,CAACmB,QAAf,EAAyBX,OAAzB,EAAkCyB,KAAlC,EAAyC,KAAzC,CAAV;AACD;AACF,OA9BD,MA8BO,IAAIQ,YAAY,CAACR,KAAD,CAAhB,EAAyB;AAC9B,YAAI,CAACe,gBAAgB,CAACI,CAAD,CAArB,EAA0B;AACxBJ,UAAAA,gBAAgB,CAACI,CAAD,CAAhB,GAAsBlD,mBAAmB,CAAC2D,MAApB,CAA2B5B,KAAK,CAACqB,UAAjC,CAAtB;AACD;;AAED,YAAIrB,KAAK,CAACH,SAAN,KAAoB1B,cAAc,CAACmD,YAAvC,EAAqD;AACnDvB,UAAAA,UAAU,CACRH,4BAA4B,CAACI,KAAK,CAACH,SAAP,CADpB,EAERtB,OAFQ,EAGRyB,KAHQ,EAIRe,gBAAgB,CAACI,CAAD,CAJR,CAAV;AAMD;AACF,OAbM,MAaA;AACLpB,QAAAA,UAAU,CAAChC,aAAa,CAACa,MAAf,EAAuBL,OAAvB,EAAgCyB,KAAhC,CAAV;;AAEA,YAAIzB,OAAO,CAACQ,QAAR,IAAoBR,OAAO,CAACsD,qBAAhC,EAAuD;AAAA;;AACrD9B,UAAAA,UAAU,CACRhC,aAAa,CAACe,MADN,EAERP,OAFQ,2BAGRA,OAAO,CAACsD,qBAHA,0DAGR,2BAAAtD,OAAO,EACLyB,KADK,EAELc,eAAe,CAACI,KAAhB,CAAsB3C,OAAO,CAAC8C,UAA9B,CAFK,CAHC,CAAV;AASAP,UAAAA,eAAe,CAACI,KAAhB,CAAsB3C,OAAO,CAAC8C,UAA9B,IAA4CrB,KAA5C;AACD;AACF;AACF;AACF,GA7ED,CApBA,CAmGA;;;AACA,QAAMA,KAAK,GAAGhC,UAAU,CAAC8D,QAAX,CACZd,QADY,EAEZ,CAAC,6BAAD,EAAgC,uBAAhC,CAFY,EAGZL,YAHY,CAAd;AAMAD,EAAAA,eAAe,CAACqB,oBAAhB,GAAuC/B,KAAvC;AACAU,EAAAA,eAAe,CAACsB,gBAAhB,GAAmCpB,uBAAnC;AACD", "sourcesContent": ["import { HandlerCallbacks, CALLBACK_TYPE } from '../gesture';\nimport { Reanimated } from '../reanimatedWrapper';\nimport {\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from '../../gestureHandlerCommon';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from '../gestureStateManager';\nimport { State } from '../../../State';\nimport { TouchEventType } from '../../../TouchEventType';\nimport { tagMessage } from '../../../utils';\nimport { AttachedGestureState } from './types';\n\nfunction getHandler(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>\n) {\n  'worklet';\n  switch (type) {\n    case CALLBACK_TYPE.BEGAN:\n      return gesture.onBegin;\n    case CALLBACK_TYPE.START:\n      return gesture.onStart;\n    case CALLBACK_TYPE.UPDATE:\n      return gesture.onUpdate;\n    case CALLBACK_TYPE.CHANGE:\n      return gesture.onChange;\n    case CALLBACK_TYPE.END:\n      return gesture.onEnd;\n    case CALLBACK_TYPE.FINALIZE:\n      return gesture.onFinalize;\n    case CALLBACK_TYPE.TOUCHES_DOWN:\n      return gesture.onTouchesDown;\n    case CALLBACK_TYPE.TOUCHES_MOVE:\n      return gesture.onTouchesMove;\n    case CALLBACK_TYPE.TOUCHES_UP:\n      return gesture.onTouchesUp;\n    case CALLBACK_TYPE.TOUCHES_CANCELLED:\n      return gesture.onTouchesCancelled;\n  }\n}\n\nfunction touchEventTypeToCallbackType(\n  eventType: TouchEventType\n): CALLBACK_TYPE {\n  'worklet';\n  switch (eventType) {\n    case TouchEventType.TOUCHES_DOWN:\n      return CALLBACK_TYPE.TOUCHES_DOWN;\n    case TouchEventType.TOUCHES_MOVE:\n      return CALLBACK_TYPE.TOUCHES_MOVE;\n    case TouchEventType.TOUCHES_UP:\n      return CALLBACK_TYPE.TOUCHES_UP;\n    case TouchEventType.TOUCHES_CANCELLED:\n      return CALLBACK_TYPE.TOUCHES_CANCELLED;\n  }\n  return CALLBACK_TYPE.UNDEFINED;\n}\n\nfunction runWorklet(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>,\n  event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent,\n  ...args: unknown[]\n) {\n  'worklet';\n  const handler = getHandler(type, gesture);\n  if (gesture.isWorklet[type]) {\n    // @ts-ignore Logic below makes sure the correct event is send to the\n    // correct handler.\n    handler?.(event, ...args);\n  } else if (handler) {\n    console.warn(tagMessage('Animated gesture callback must be a worklet'));\n  }\n}\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  'worklet';\n  // @ts-ignore Yes, the oldState prop is missing on GestureTouchEvent, that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  'worklet';\n  return event.eventType != null;\n}\n\nexport function useAnimatedGesture(\n  preparedGesture: AttachedGestureState,\n  needsRebuild: boolean\n) {\n  if (!Reanimated) {\n    return;\n  }\n\n  // Hooks are called conditionally, but the condition is whether the\n  // react-native-reanimated is installed, which shouldn't change while running\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const sharedHandlersCallbacks = Reanimated.useSharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  >(null);\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const lastUpdateEvent = Reanimated.useSharedValue<\n    (GestureUpdateEvent | undefined)[]\n  >([]);\n\n  // not every gesture needs a state controller, init them lazily\n  const stateControllers: GestureStateManagerType[] = [];\n\n  const callback = (\n    event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent\n  ) => {\n    'worklet';\n\n    const currentCallback = sharedHandlersCallbacks.value;\n    if (!currentCallback) {\n      return;\n    }\n\n    for (let i = 0; i < currentCallback.length; i++) {\n      const gesture = currentCallback[i];\n\n      if (event.handlerTag !== gesture.handlerTag) {\n        continue;\n      }\n\n      if (isStateChangeEvent(event)) {\n        if (\n          event.oldState === State.UNDETERMINED &&\n          event.state === State.BEGAN\n        ) {\n          runWorklet(CALLBACK_TYPE.BEGAN, gesture, event);\n        } else if (\n          (event.oldState === State.BEGAN ||\n            event.oldState === State.UNDETERMINED) &&\n          event.state === State.ACTIVE\n        ) {\n          runWorklet(CALLBACK_TYPE.START, gesture, event);\n          lastUpdateEvent.value[gesture.handlerTag] = undefined;\n        } else if (\n          event.oldState !== event.state &&\n          event.state === State.END\n        ) {\n          if (event.oldState === State.ACTIVE) {\n            runWorklet(CALLBACK_TYPE.END, gesture, event, true);\n          }\n          runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, true);\n        } else if (\n          (event.state === State.FAILED || event.state === State.CANCELLED) &&\n          event.state !== event.oldState\n        ) {\n          if (event.oldState === State.ACTIVE) {\n            runWorklet(CALLBACK_TYPE.END, gesture, event, false);\n          }\n          runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, false);\n        }\n      } else if (isTouchEvent(event)) {\n        if (!stateControllers[i]) {\n          stateControllers[i] = GestureStateManager.create(event.handlerTag);\n        }\n\n        if (event.eventType !== TouchEventType.UNDETERMINED) {\n          runWorklet(\n            touchEventTypeToCallbackType(event.eventType),\n            gesture,\n            event,\n            stateControllers[i]\n          );\n        }\n      } else {\n        runWorklet(CALLBACK_TYPE.UPDATE, gesture, event);\n\n        if (gesture.onChange && gesture.changeEventCalculator) {\n          runWorklet(\n            CALLBACK_TYPE.CHANGE,\n            gesture,\n            gesture.changeEventCalculator?.(\n              event,\n              lastUpdateEvent.value[gesture.handlerTag]\n            )\n          );\n\n          lastUpdateEvent.value[gesture.handlerTag] = event;\n        }\n      }\n    }\n  };\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const event = Reanimated.useEvent(\n    callback,\n    ['onGestureHandlerStateChange', 'onGestureHandlerEvent'],\n    needsRebuild\n  );\n\n  preparedGesture.animatedEventHandler = event;\n  preparedGesture.animatedHandlers = sharedHandlersCallbacks;\n}\n"]}