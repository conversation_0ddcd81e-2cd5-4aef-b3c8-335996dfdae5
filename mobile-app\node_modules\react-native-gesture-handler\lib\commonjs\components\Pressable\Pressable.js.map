{"version": 3, "sources": ["Pressable.tsx"], "names": ["DEFAULT_LONG_PRESS_DURATION", "IS_TEST_ENV", "IS_FABRIC", "Pressable", "props", "pressableRef", "testOnly_pressed", "hitSlop", "pressRetentionOffset", "delayHoverIn", "onHoverIn", "delayHoverOut", "onHoverOut", "delayLongPress", "unstable_pressDelay", "onPress", "onPressIn", "onPressOut", "onLongPress", "style", "children", "android_disableSound", "android_ripple", "disabled", "accessible", "remainingProps", "pressedState", "setPressedState", "isPressCallbackEnabled", "hasPassedBoundsChecks", "shouldPreventNativeEffects", "normalizedHitSlop", "normalizedPressRetentionOffset", "hoverInTimeout", "hoverOutTimeout", "hoverGesture", "Gesture", "Hover", "manualActivation", "cancelsTouchesInView", "onBegin", "event", "current", "clearTimeout", "setTimeout", "onFinalize", "pressDelayTimeoutRef", "isTouchPropagationAllowed", "deferredEventPayload", "pressInHandler", "handlingOnTouchesDown", "pressOutHandler", "longPressTimeoutRef", "nativeEvent", "touches", "length", "changedTouches", "onEndHandlingTouchesDown", "cancelledMidPress", "activateLongPress", "longPressMinDuration", "innerPressableRef", "measureCallback", "width", "height", "at", "pressAndTouchGesture", "Long<PERSON>ress", "minDuration", "INT32_MAX", "maxDistance", "onTouchesDown", "measure", "_x", "_y", "onTouchesUp", "onTouchesCancelled", "allTouches", "buttonGesture", "Native", "Platform", "OS", "onStart", "appliedHitSlop", "isPressableEnabled", "gestures", "gesture", "enabled", "runOnJS", "shouldCancelWhenOutside", "Simultaneous", "pointerStyle", "cursor", "styleProp", "pressed", "childrenProp", "rippleColor", "defaultRippleColor", "undefined", "unprocessedRippleColor", "color", "radius", "__DEV__"], "mappings": ";;;;;;;AAAA;;AASA;;AACA;;AAEA;;AAQA;;AACA;;AAOA;;AAEA;;;;;;;;;;AAEA,MAAMA,2BAA2B,GAAG,GAApC;AACA,MAAMC,WAAW,GAAG,wBAApB;AAEA,IAAIC,SAAyB,GAAG,IAAhC;AAEA,MAAMC,SAAS,gBAAG,uBAChB,CAACC,KAAD,EAAwBC,YAAxB,KAA6D;AAAA;;AAC3D,QAAM;AACJC,IAAAA,gBADI;AAEJC,IAAAA,OAFI;AAGJC,IAAAA,oBAHI;AAIJC,IAAAA,YAJI;AAKJC,IAAAA,SALI;AAMJC,IAAAA,aANI;AAOJC,IAAAA,UAPI;AAQJC,IAAAA,cARI;AASJC,IAAAA,mBATI;AAUJC,IAAAA,OAVI;AAWJC,IAAAA,SAXI;AAYJC,IAAAA,UAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,KAdI;AAeJC,IAAAA,QAfI;AAgBJC,IAAAA,oBAhBI;AAiBJC,IAAAA,cAjBI;AAkBJC,IAAAA,QAlBI;AAmBJC,IAAAA,UAnBI;AAoBJ,OAAGC;AApBC,MAqBFrB,KArBJ;AAuBA,QAAM,CAACsB,YAAD,EAAeC,eAAf,IAAkC,qBAASrB,gBAAT,aAASA,gBAAT,cAASA,gBAAT,GAA6B,KAA7B,CAAxC,CAxB2D,CA0B3D;;AACA,QAAMsB,sBAAsB,GAAG,mBAAgB,IAAhB,CAA/B;AACA,QAAMC,qBAAqB,GAAG,mBAAgB,KAAhB,CAA9B;AACA,QAAMC,0BAA0B,GAAG,mBAAgB,KAAhB,CAAnC;AAEA,QAAMC,iBAAyB,GAAG,oBAChC,MACE,OAAOxB,OAAP,KAAmB,QAAnB,GAA8B,0BAAcA,OAAd,CAA9B,GAAwDA,OAAxD,aAAwDA,OAAxD,cAAwDA,OAAxD,GAAmE,EAFrC,EAGhC,CAACA,OAAD,CAHgC,CAAlC;AAMA,QAAMyB,8BAAsC,GAAG,oBAC7C,MACE,OAAOxB,oBAAP,KAAgC,QAAhC,GACI,0BAAcA,oBAAd,CADJ,GAEKA,oBAFL,aAEKA,oBAFL,cAEKA,oBAFL,GAE6B,EAJc,EAK7C,CAACA,oBAAD,CAL6C,CAA/C;AAQA,QAAMyB,cAAc,GAAG,mBAAsB,IAAtB,CAAvB;AACA,QAAMC,eAAe,GAAG,mBAAsB,IAAtB,CAAxB;AAEA,QAAMC,YAAY,GAAG,oBACnB,MACEC,+BAAQC,KAAR,GACGC,gBADH,CACoB,IADpB,EAC0B;AAD1B,GAEGC,oBAFH,CAEwB,KAFxB,EAGGC,OAHH,CAGYC,KAAD,IAAW;AAClB,QAAIP,eAAe,CAACQ,OAApB,EAA6B;AAC3BC,MAAAA,YAAY,CAACT,eAAe,CAACQ,OAAjB,CAAZ;AACD;;AACD,QAAIjC,YAAJ,EAAkB;AAChBwB,MAAAA,cAAc,CAACS,OAAf,GAAyBE,UAAU,CACjC,MAAMlC,SAAN,aAAMA,SAAN,uBAAMA,SAAS,CAAG,oCAAwB+B,KAAxB,CAAH,CADkB,EAEjChC,YAFiC,CAAnC;AAIA;AACD;;AACDC,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAG,oCAAwB+B,KAAxB,CAAH,CAAT;AACD,GAfH,EAgBGI,UAhBH,CAgBeJ,KAAD,IAAW;AACrB,QAAIR,cAAc,CAACS,OAAnB,EAA4B;AAC1BC,MAAAA,YAAY,CAACV,cAAc,CAACS,OAAhB,CAAZ;AACD;;AACD,QAAI/B,aAAJ,EAAmB;AACjBuB,MAAAA,eAAe,CAACQ,OAAhB,GAA0BE,UAAU,CAClC,MAAMhC,UAAN,aAAMA,UAAN,uBAAMA,UAAU,CAAG,oCAAwB6B,KAAxB,CAAH,CADkB,EAElC9B,aAFkC,CAApC;AAIA;AACD;;AACDC,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAG,oCAAwB6B,KAAxB,CAAH,CAAV;AACD,GA5BH,CAFiB,EA+BnB,CAAChC,YAAD,EAAeE,aAAf,EAA8BD,SAA9B,EAAyCE,UAAzC,CA/BmB,CAArB;AAkCA,QAAMkC,oBAAoB,GAAG,mBAAsB,IAAtB,CAA7B;AACA,QAAMC,yBAAyB,GAAG,mBAAgB,KAAhB,CAAlC,CAnF2D,CAqF3D;;AACA,QAAMC,oBAAoB,GAAG,mBAA8B,IAA9B,CAA7B;AAEA,QAAMC,cAAc,GAAG,wBACpBR,KAAD,IAA2B;AACzB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCM,MAAAA,oBAAoB,CAACN,OAArB,GAA+BD,KAA/B;AACD;;AAED,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAEDM,IAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AAEA1B,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGyB,KAAH,CAAT;AACAb,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,IAAjC;AACAI,IAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACAf,IAAAA,eAAe,CAAC,IAAD,CAAf;AACD,GAhBoB,EAiBrB,CAACX,SAAD,CAjBqB,CAAvB;AAoBA,QAAMmC,eAAe,GAAG,wBACrBV,KAAD,IAA2B;AACzB,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtCb,MAAAA,qBAAqB,CAACa,OAAtB,GAAgC,KAAhC;AACAd,MAAAA,sBAAsB,CAACc,OAAvB,GAAiC,IAAjC;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;;AAEA,UAAIU,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,QAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,QAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;;AAED,UAAII,oBAAoB,CAACJ,OAAzB,EAAkC;AAChCC,QAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAI,QAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACD;;AAED;AACD;;AAED,QACE,CAACb,qBAAqB,CAACa,OAAvB,IACAD,KAAK,CAACY,WAAN,CAAkBC,OAAlB,CAA0BC,MAA1B,GACEd,KAAK,CAACY,WAAN,CAAkBG,cAAlB,CAAiCD,MAHrC,EAIE;AACA;AACD;;AAED,QAAIzC,mBAAmB,IAAIgC,oBAAoB,CAACJ,OAArB,KAAiC,IAA5D,EAAkE;AAChE;AACA;AACA;AACAC,MAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAO,MAAAA,cAAc,CAACR,KAAD,CAAd;AACD;;AAED,QAAIO,oBAAoB,CAACN,OAAzB,EAAkC;AAChC1B,MAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGgC,oBAAoB,CAACN,OAAxB,CAAT;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD;;AAEDzB,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGwB,KAAH,CAAV;;AAEA,QAAIb,sBAAsB,CAACc,OAA3B,EAAoC;AAClC3B,MAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAG0B,KAAH,CAAP;AACD;;AAED,QAAIW,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACAb,IAAAA,qBAAqB,CAACa,OAAtB,GAAgC,KAAhC;AACAd,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,IAAjC;AACAf,IAAAA,eAAe,CAAC,KAAD,CAAf;AACD,GAxDqB,EAyDtB,CAACZ,OAAD,EAAUC,SAAV,EAAqBC,UAArB,EAAiCgC,cAAjC,EAAiDnC,mBAAjD,CAzDsB,CAAxB;AA4DA,QAAMoC,qBAAqB,GAAG,mBAAgB,KAAhB,CAA9B;AACA,QAAMO,wBAAwB,GAAG,mBAA4B,IAA5B,CAAjC;AACA,QAAMC,iBAAiB,GAAG,mBAAgB,KAAhB,CAA1B;AAEA,QAAMC,iBAAiB,GAAG,wBACvBlB,KAAD,IAA8B;AAC5B,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAED,QAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCxB,MAAAA,WAAW,SAAX,IAAAA,WAAW,WAAX,YAAAA,WAAW,CAAG,yCAA6BuB,KAA7B,CAAH,CAAX;AACAb,MAAAA,sBAAsB,CAACc,OAAvB,GAAiC,KAAjC;AACD;;AAED,QAAIU,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;AACF,GAfuB,EAgBxB,CAACxB,WAAD,CAhBwB,CAA1B;AAmBA,QAAMkC,mBAAmB,GAAG,mBAAsB,IAAtB,CAA5B;AACA,QAAMQ,oBAAoB,GACxB,CAAC/C,cAAD,aAACA,cAAD,cAACA,cAAD,GAAmBb,2BAAnB,KACCc,mBADD,aACCA,mBADD,cACCA,mBADD,GACwB,CADxB,CADF;AAIA,QAAM+C,iBAAiB,GAAG,mBAAa,IAAb,CAA1B;AAEA,QAAMC,eAAe,GAAG,wBACtB,CAACC,KAAD,EAAgBC,MAAhB,EAAgCvB,KAAhC,KAA6D;AAAA;;AAC3D,QACE,CAAC,+BACC;AACEsB,MAAAA,KADF;AAEEC,MAAAA;AAFF,KADD,EAKCjC,iBALD,EAMCU,KAAK,CAACe,cAAN,CAAqBS,EAArB,CAAwB,CAAC,CAAzB,CAND,CAAD,IAQApC,qBAAqB,CAACa,OARtB,IASAgB,iBAAiB,CAAChB,OAVpB,EAWE;AACAgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,KAA5B;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,MAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACA;AACD;;AAEDb,IAAAA,qBAAqB,CAACa,OAAtB,GAAgC,IAAhC,CAnB2D,CAqB3D;;AACA,QAAIU,mBAAmB,CAACV,OAApB,KAAgC,IAApC,EAA0C;AACxC;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8BE,UAAU,CACtC,MAAMe,iBAAiB,CAAClB,KAAD,CADe,EAEtCmB,oBAFsC,CAAxC;AAID;;AAED,QAAI9C,mBAAJ,EAAyB;AACvBgC,MAAAA,oBAAoB,CAACJ,OAArB,GAA+BE,UAAU,CAAC,MAAM;AAC9CK,QAAAA,cAAc,CAAC,yCAA6BR,KAA7B,CAAD,CAAd;AACD,OAFwC,EAEtC3B,mBAFsC,CAAzC;AAGD,KAJD,MAIO;AACLmC,MAAAA,cAAc,CAAC,yCAA6BR,KAA7B,CAAD,CAAd;AACD;;AAED,6BAAAgB,wBAAwB,CAACf,OAAzB,qFAAAe,wBAAwB;AACxBA,IAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACD,GA1CqB,EA2CtB,CACEiB,iBADF,EAEEC,oBAFF,EAGE7B,iBAHF,EAIEkB,cAJF,EAKEnC,mBALF,CA3CsB,CAAxB;AAoDA,QAAMoD,oBAAoB,GAAG,oBAC3B,MACE9B,+BAAQ+B,SAAR,GACGC,WADH,CACeC,iBADf,EAC0B;AAD1B,GAEGC,WAFH,CAEeD,iBAFf,EAE0B;AAF1B,GAGG9B,oBAHH,CAGwB,KAHxB,EAIGgC,aAJH,CAIkB9B,KAAD,IAAW;AACxBS,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,IAAhC;;AACA,QAAIrC,YAAJ,EAAkB;AAAA;;AAChB,kBAACA,YAAD,CAAkCqC,OAAlC,sDAA2C8B,OAA3C,CACE,CAACC,EAAD,EAAKC,EAAL,EAASX,KAAT,EAAgBC,MAAhB,KAA2B;AACzBF,QAAAA,eAAe,CAACC,KAAD,EAAQC,MAAR,EAAgBvB,KAAhB,CAAf;AACD,OAHH;AAKD,KAND,MAMO;AAAA;;AACL,+BAAAoB,iBAAiB,CAACnB,OAAlB,gFAA2B8B,OAA3B,CAAmC,CAACC,EAAD,EAAKC,EAAL,EAASX,KAAT,EAAgBC,MAAhB,KAA2B;AAC5DF,QAAAA,eAAe,CAACC,KAAD,EAAQC,MAAR,EAAgBvB,KAAhB,CAAf;AACD,OAFD;AAGD;AACF,GAjBH,EAkBGkC,WAlBH,CAkBgBlC,KAAD,IAAW;AACtB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CADjB;;AAEA;AACD,KALqB,CAMtB;AACA;;;AACA,QAAIO,oBAAoB,CAACN,OAArB,KAAiC,IAArC,EAA2C;AACzCZ,MAAAA,0BAA0B,CAACY,OAA3B,GAAqC,IAArC;AACD;;AACDS,IAAAA,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CAAf;AACD,GA9BH,EA+BGmC,kBA/BH,CA+BuBnC,KAAD,IAAW;AAC7Bb,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,KAAjC;;AAEA,QAAIQ,qBAAqB,CAACR,OAA1B,EAAmC;AACjCgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,IAA5B;;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CADjB;;AAEA;AACD;;AAED,QACE,CAACZ,qBAAqB,CAACa,OAAvB,IACAD,KAAK,CAACoC,UAAN,CAAiBtB,MAAjB,GAA0Bd,KAAK,CAACe,cAAN,CAAqBD,MAFjD,EAGE;AACA;AACD;;AAEDJ,IAAAA,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CAAf;AACD,GAjDH,CAFyB,EAoD3B,CAACpC,YAAD,EAAeyD,eAAf,EAAgCX,eAAhC,CApD2B,CAA7B,CA1P2D,CAiT3D;;AACA,QAAM2B,aAAa,GAAG,oBACpB,MACE1C,+BAAQ2C,MAAR,GACGvC,OADH,CACW,MAAM;AACb;AACA,QAAIwC,sBAASC,EAAT,KAAgB,SAAhB,IAA6BD,sBAASC,EAAT,KAAgB,OAAjD,EAA0D;AACxDlC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD;AACF,GANH,EAOGwC,OAPH,CAOW,MAAM;AACb,QAAIF,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzBlC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,KAHY,CAKb;;;AACA,QAAIsC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,QAAIjC,oBAAoB,CAACN,OAAzB,EAAkC;AAChCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;;AAEA,UAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCO,QAAAA,cAAc,CAACD,oBAAoB,CAACN,OAAtB,CAAd;AACAM,QAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD,OAHD,MAGO;AACLS,QAAAA,eAAe,CAACH,oBAAoB,CAACN,OAAtB,CAAf;AACAK,QAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACD;;AAED;AACD;;AAED,QAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACA;AACD;;AAED,QAAIZ,0BAA0B,CAACY,OAA/B,EAAwC;AACtCZ,MAAAA,0BAA0B,CAACY,OAA3B,GAAqC,KAArC;;AACA,UAAI,CAACQ,qBAAqB,CAACR,OAA3B,EAAoC;AAClC;AACD;AACF;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,GA5CH,CAFkB,EA+CpB,CAACO,cAAD,EAAiBE,eAAjB,CA/CoB,CAAtB;AAkDA,QAAMgC,cAAc,GAAG,sBACrBpD,iBADqB,EAErBC,8BAFqB,CAAvB;AAKA,QAAMoD,kBAAkB,GAAG7D,QAAQ,KAAK,IAAxC;AAEA,QAAM8D,QAAQ,GAAG,CAACP,aAAD,EAAgBZ,oBAAhB,EAAsC/B,YAAtC,CAAjB;;AAEA,OAAK,MAAMmD,OAAX,IAAsBD,QAAtB,EAAgC;AAC9BC,IAAAA,OAAO,CAACC,OAAR,CAAgBH,kBAAhB;AACAE,IAAAA,OAAO,CAACE,OAAR,CAAgB,IAAhB;AACAF,IAAAA,OAAO,CAAC/E,OAAR,CAAgB4E,cAAhB;AACAG,IAAAA,OAAO,CAACG,uBAAR,CAAgCT,sBAASC,EAAT,KAAgB,KAAhB,GAAwB,KAAxB,GAAgC,IAAhE;AACD,GAlX0D,CAoX3D;;;AACAH,EAAAA,aAAa,CAACvE,OAAd,CAAsBwB,iBAAtB;;AAEA,QAAMuD,OAAO,GAAGlD,+BAAQsD,YAAR,CAAqB,GAAGL,QAAxB,CAAhB,CAvX2D,CAyX3D;;;AACA,QAAMM,YAAkC,GACtCX,sBAASC,EAAT,KAAgB,KAAhB,GAAwB;AAAEW,IAAAA,MAAM,EAAE;AAAV,GAAxB,GAAgD,EADlD;AAGA,QAAMC,SAAS,GACb,OAAO1E,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC;AAAE2E,IAAAA,OAAO,EAAEpE;AAAX,GAAD,CAAnC,GAAiEP,KADnE;AAGA,QAAM4E,YAAY,GAChB,OAAO3E,QAAP,KAAoB,UAApB,GACIA,QAAQ,CAAC;AAAE0E,IAAAA,OAAO,EAAEpE;AAAX,GAAD,CADZ,GAEIN,QAHN;AAKA,QAAM4E,WAAW,GAAG,oBAAQ,MAAM;AAAA;;AAChC,QAAI9F,SAAS,KAAK,IAAlB,EAAwB;AACtBA,MAAAA,SAAS,GAAG,uBAAZ;AACD;;AAED,UAAM+F,kBAAkB,GAAG3E,cAAc,GAAG4E,SAAH,GAAe,aAAxD;AACA,UAAMC,sBAAsB,4BAC1B7E,cAD0B,aAC1BA,cAD0B,uBAC1BA,cAAc,CAAE8E,KADU,yEACDH,kBAD3B;AAEA,WAAO/F,SAAS,GACZiG,sBADY,GAEZ,+BAAaA,sBAAb,CAFJ;AAGD,GAXmB,EAWjB,CAAC7E,cAAD,CAXiB,CAApB;AAaA,sBACE,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAEgE;AAA1B,kBACE,6BAAC,6BAAD,eACM7D,cADN;AAEE,IAAA,GAAG,EAAEpB,YAAF,aAAEA,YAAF,cAAEA,YAAF,GAAkBwD,iBAFvB;AAGE,IAAA,UAAU,EAAErC,UAAU,KAAK,KAH7B;AAIE,IAAA,OAAO,EAAE2D,cAJX;AAKE,IAAA,OAAO,EAAEC,kBALX;AAME,IAAA,kBAAkB,EAAE/D,oBAAF,aAAEA,oBAAF,cAAEA,oBAAF,GAA0B6E,SAN9C;AAOE,IAAA,WAAW,EAAEF,WAPf;AAQE,IAAA,YAAY,2BAAE1E,cAAF,aAAEA,cAAF,uBAAEA,cAAc,CAAE+E,MAAlB,yEAA4BH,SAR1C;AASE,IAAA,KAAK,EAAE,CAACP,YAAD,EAAeE,SAAf,CATT;AAUE,IAAA,gBAAgB,EAAE5F,WAAW,GAAGc,OAAH,GAAamF,SAV5C;AAWE,IAAA,kBAAkB,EAAEjG,WAAW,GAAGe,SAAH,GAAekF,SAXhD;AAYE,IAAA,mBAAmB,EAAEjG,WAAW,GAAGgB,UAAH,GAAgBiF,SAZlD;AAaE,IAAA,oBAAoB,EAAEjG,WAAW,GAAGiB,WAAH,GAAiBgF;AAbpD,MAcGH,YAdH,EAeGO,OAAO,gBACN,6BAAC,4CAAD;AAAuB,IAAA,KAAK,EAAC,KAA7B;AAAmC,IAAA,OAAO,EAAEvE;AAA5C,IADM,GAEJ,IAjBN,CADF,CADF;AAuBD,CA1ae,CAAlB;eA6ae5B,S", "sourcesContent": ["import React, {\n  ForwardedRef,\n  forwardRef,\n  RefObject,\n  useCallback,\n  useMemo,\n  useRef,\n  useState,\n} from 'react';\nimport { GestureObjects as Gesture } from '../../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../../handlers/gestures/GestureDetector';\nimport { PressableEvent, PressableProps } from './PressableProps';\nimport {\n  Insets,\n  Platform,\n  StyleProp,\n  View,\n  ViewStyle,\n  processColor,\n} from 'react-native';\nimport NativeButton from '../GestureHandlerButton';\nimport {\n  numberAsInset,\n  gestureToPressableEvent,\n  isTouchWithinInset,\n  gestureTouchToPressableEvent,\n  addInsets,\n} from './utils';\nimport { PressabilityDebugView } from '../../handlers/PressabilityDebugView';\nimport { GestureTouchEvent } from '../../handlers/gestureHandlerCommon';\nimport { INT32_MAX, isFabric, isTestEnv } from '../../utils';\n\nconst DEFAULT_LONG_PRESS_DURATION = 500;\nconst IS_TEST_ENV = isTestEnv();\n\nlet IS_FABRIC: null | boolean = null;\n\nconst Pressable = forwardRef(\n  (props: PressableProps, pressableRef: ForwardedRef<View>) => {\n    const {\n      testOnly_pressed,\n      hitSlop,\n      pressRetentionOffset,\n      delayHoverIn,\n      onHoverIn,\n      delayHoverOut,\n      onHoverOut,\n      delayLongPress,\n      unstable_pressDelay,\n      onPress,\n      onPressIn,\n      onPressOut,\n      onLongPress,\n      style,\n      children,\n      android_disableSound,\n      android_ripple,\n      disabled,\n      accessible,\n      ...remainingProps\n    } = props;\n\n    const [pressedState, setPressedState] = useState(testOnly_pressed ?? false);\n\n    // Disabled when onLongPress has been called\n    const isPressCallbackEnabled = useRef<boolean>(true);\n    const hasPassedBoundsChecks = useRef<boolean>(false);\n    const shouldPreventNativeEffects = useRef<boolean>(false);\n\n    const normalizedHitSlop: Insets = useMemo(\n      () =>\n        typeof hitSlop === 'number' ? numberAsInset(hitSlop) : (hitSlop ?? {}),\n      [hitSlop]\n    );\n\n    const normalizedPressRetentionOffset: Insets = useMemo(\n      () =>\n        typeof pressRetentionOffset === 'number'\n          ? numberAsInset(pressRetentionOffset)\n          : (pressRetentionOffset ?? {}),\n      [pressRetentionOffset]\n    );\n\n    const hoverInTimeout = useRef<number | null>(null);\n    const hoverOutTimeout = useRef<number | null>(null);\n\n    const hoverGesture = useMemo(\n      () =>\n        Gesture.Hover()\n          .manualActivation(true) // Stops Hover from blocking Native gesture activation on web\n          .cancelsTouchesInView(false)\n          .onBegin((event) => {\n            if (hoverOutTimeout.current) {\n              clearTimeout(hoverOutTimeout.current);\n            }\n            if (delayHoverIn) {\n              hoverInTimeout.current = setTimeout(\n                () => onHoverIn?.(gestureToPressableEvent(event)),\n                delayHoverIn\n              );\n              return;\n            }\n            onHoverIn?.(gestureToPressableEvent(event));\n          })\n          .onFinalize((event) => {\n            if (hoverInTimeout.current) {\n              clearTimeout(hoverInTimeout.current);\n            }\n            if (delayHoverOut) {\n              hoverOutTimeout.current = setTimeout(\n                () => onHoverOut?.(gestureToPressableEvent(event)),\n                delayHoverOut\n              );\n              return;\n            }\n            onHoverOut?.(gestureToPressableEvent(event));\n          }),\n      [delayHoverIn, delayHoverOut, onHoverIn, onHoverOut]\n    );\n\n    const pressDelayTimeoutRef = useRef<number | null>(null);\n    const isTouchPropagationAllowed = useRef<boolean>(false);\n\n    // iOS only: due to varying flow of gestures, events sometimes have to be saved for later use\n    const deferredEventPayload = useRef<PressableEvent | null>(null);\n\n    const pressInHandler = useCallback(\n      (event: PressableEvent) => {\n        if (handlingOnTouchesDown.current) {\n          deferredEventPayload.current = event;\n        }\n\n        if (!isTouchPropagationAllowed.current) {\n          return;\n        }\n\n        deferredEventPayload.current = null;\n\n        onPressIn?.(event);\n        isPressCallbackEnabled.current = true;\n        pressDelayTimeoutRef.current = null;\n        setPressedState(true);\n      },\n      [onPressIn]\n    );\n\n    const pressOutHandler = useCallback(\n      (event: PressableEvent) => {\n        if (!isTouchPropagationAllowed.current) {\n          hasPassedBoundsChecks.current = false;\n          isPressCallbackEnabled.current = true;\n          deferredEventPayload.current = null;\n\n          if (longPressTimeoutRef.current) {\n            clearTimeout(longPressTimeoutRef.current);\n            longPressTimeoutRef.current = null;\n          }\n\n          if (pressDelayTimeoutRef.current) {\n            clearTimeout(pressDelayTimeoutRef.current);\n            pressDelayTimeoutRef.current = null;\n          }\n\n          return;\n        }\n\n        if (\n          !hasPassedBoundsChecks.current ||\n          event.nativeEvent.touches.length >\n            event.nativeEvent.changedTouches.length\n        ) {\n          return;\n        }\n\n        if (unstable_pressDelay && pressDelayTimeoutRef.current !== null) {\n          // When delay is preemptively finished by lifting touches,\n          // we want to immediately activate it's effects - pressInHandler,\n          // even though we are located at the pressOutHandler\n          clearTimeout(pressDelayTimeoutRef.current);\n          pressInHandler(event);\n        }\n\n        if (deferredEventPayload.current) {\n          onPressIn?.(deferredEventPayload.current);\n          deferredEventPayload.current = null;\n        }\n\n        onPressOut?.(event);\n\n        if (isPressCallbackEnabled.current) {\n          onPress?.(event);\n        }\n\n        if (longPressTimeoutRef.current) {\n          clearTimeout(longPressTimeoutRef.current);\n          longPressTimeoutRef.current = null;\n        }\n\n        isTouchPropagationAllowed.current = false;\n        hasPassedBoundsChecks.current = false;\n        isPressCallbackEnabled.current = true;\n        setPressedState(false);\n      },\n      [onPress, onPressIn, onPressOut, pressInHandler, unstable_pressDelay]\n    );\n\n    const handlingOnTouchesDown = useRef<boolean>(false);\n    const onEndHandlingTouchesDown = useRef<(() => void) | null>(null);\n    const cancelledMidPress = useRef<boolean>(false);\n\n    const activateLongPress = useCallback(\n      (event: GestureTouchEvent) => {\n        if (!isTouchPropagationAllowed.current) {\n          return;\n        }\n\n        if (hasPassedBoundsChecks.current) {\n          onLongPress?.(gestureTouchToPressableEvent(event));\n          isPressCallbackEnabled.current = false;\n        }\n\n        if (longPressTimeoutRef.current) {\n          clearTimeout(longPressTimeoutRef.current);\n          longPressTimeoutRef.current = null;\n        }\n      },\n      [onLongPress]\n    );\n\n    const longPressTimeoutRef = useRef<number | null>(null);\n    const longPressMinDuration =\n      (delayLongPress ?? DEFAULT_LONG_PRESS_DURATION) +\n      (unstable_pressDelay ?? 0);\n\n    const innerPressableRef = useRef<View>(null);\n\n    const measureCallback = useCallback(\n      (width: number, height: number, event: GestureTouchEvent) => {\n        if (\n          !isTouchWithinInset(\n            {\n              width,\n              height,\n            },\n            normalizedHitSlop,\n            event.changedTouches.at(-1)\n          ) ||\n          hasPassedBoundsChecks.current ||\n          cancelledMidPress.current\n        ) {\n          cancelledMidPress.current = false;\n          onEndHandlingTouchesDown.current = null;\n          handlingOnTouchesDown.current = false;\n          return;\n        }\n\n        hasPassedBoundsChecks.current = true;\n\n        // In case of multiple touches, the first one starts long press gesture\n        if (longPressTimeoutRef.current === null) {\n          // Start long press gesture timer\n          longPressTimeoutRef.current = setTimeout(\n            () => activateLongPress(event),\n            longPressMinDuration\n          );\n        }\n\n        if (unstable_pressDelay) {\n          pressDelayTimeoutRef.current = setTimeout(() => {\n            pressInHandler(gestureTouchToPressableEvent(event));\n          }, unstable_pressDelay);\n        } else {\n          pressInHandler(gestureTouchToPressableEvent(event));\n        }\n\n        onEndHandlingTouchesDown.current?.();\n        onEndHandlingTouchesDown.current = null;\n        handlingOnTouchesDown.current = false;\n      },\n      [\n        activateLongPress,\n        longPressMinDuration,\n        normalizedHitSlop,\n        pressInHandler,\n        unstable_pressDelay,\n      ]\n    );\n\n    const pressAndTouchGesture = useMemo(\n      () =>\n        Gesture.LongPress()\n          .minDuration(INT32_MAX) // Stops long press from blocking native gesture\n          .maxDistance(INT32_MAX) // Stops long press from cancelling after set distance\n          .cancelsTouchesInView(false)\n          .onTouchesDown((event) => {\n            handlingOnTouchesDown.current = true;\n            if (pressableRef) {\n              (pressableRef as RefObject<View>).current?.measure(\n                (_x, _y, width, height) => {\n                  measureCallback(width, height, event);\n                }\n              );\n            } else {\n              innerPressableRef.current?.measure((_x, _y, width, height) => {\n                measureCallback(width, height, event);\n              });\n            }\n          })\n          .onTouchesUp((event) => {\n            if (handlingOnTouchesDown.current) {\n              onEndHandlingTouchesDown.current = () =>\n                pressOutHandler(gestureTouchToPressableEvent(event));\n              return;\n            }\n            // On iOS, short taps will make LongPress gesture call onTouchesUp before Native gesture calls onStart\n            // This variable ensures that onStart isn't detected as the first gesture since Pressable is pressed.\n            if (deferredEventPayload.current !== null) {\n              shouldPreventNativeEffects.current = true;\n            }\n            pressOutHandler(gestureTouchToPressableEvent(event));\n          })\n          .onTouchesCancelled((event) => {\n            isPressCallbackEnabled.current = false;\n\n            if (handlingOnTouchesDown.current) {\n              cancelledMidPress.current = true;\n              onEndHandlingTouchesDown.current = () =>\n                pressOutHandler(gestureTouchToPressableEvent(event));\n              return;\n            }\n\n            if (\n              !hasPassedBoundsChecks.current ||\n              event.allTouches.length > event.changedTouches.length\n            ) {\n              return;\n            }\n\n            pressOutHandler(gestureTouchToPressableEvent(event));\n          }),\n      [pressableRef, measureCallback, pressOutHandler]\n    );\n\n    // RNButton is placed inside ButtonGesture to enable Android's ripple and to capture non-propagating events\n    const buttonGesture = useMemo(\n      () =>\n        Gesture.Native()\n          .onBegin(() => {\n            // Android sets BEGAN state on press down\n            if (Platform.OS === 'android' || Platform.OS === 'macos') {\n              isTouchPropagationAllowed.current = true;\n            }\n          })\n          .onStart(() => {\n            if (Platform.OS === 'web') {\n              isTouchPropagationAllowed.current = true;\n            }\n\n            // iOS sets ACTIVE state on press down\n            if (Platform.OS !== 'ios') {\n              return;\n            }\n\n            if (deferredEventPayload.current) {\n              isTouchPropagationAllowed.current = true;\n\n              if (hasPassedBoundsChecks.current) {\n                pressInHandler(deferredEventPayload.current);\n                deferredEventPayload.current = null;\n              } else {\n                pressOutHandler(deferredEventPayload.current);\n                isTouchPropagationAllowed.current = false;\n              }\n\n              return;\n            }\n\n            if (hasPassedBoundsChecks.current) {\n              isTouchPropagationAllowed.current = true;\n              return;\n            }\n\n            if (shouldPreventNativeEffects.current) {\n              shouldPreventNativeEffects.current = false;\n              if (!handlingOnTouchesDown.current) {\n                return;\n              }\n            }\n\n            isTouchPropagationAllowed.current = true;\n          }),\n      [pressInHandler, pressOutHandler]\n    );\n\n    const appliedHitSlop = addInsets(\n      normalizedHitSlop,\n      normalizedPressRetentionOffset\n    );\n\n    const isPressableEnabled = disabled !== true;\n\n    const gestures = [buttonGesture, pressAndTouchGesture, hoverGesture];\n\n    for (const gesture of gestures) {\n      gesture.enabled(isPressableEnabled);\n      gesture.runOnJS(true);\n      gesture.hitSlop(appliedHitSlop);\n      gesture.shouldCancelWhenOutside(Platform.OS === 'web' ? false : true);\n    }\n\n    // Uses different hitSlop, to activate on hitSlop area instead of pressRetentionOffset area\n    buttonGesture.hitSlop(normalizedHitSlop);\n\n    const gesture = Gesture.Simultaneous(...gestures);\n\n    // `cursor: 'pointer'` on `RNButton` crashes iOS\n    const pointerStyle: StyleProp<ViewStyle> =\n      Platform.OS === 'web' ? { cursor: 'pointer' } : {};\n\n    const styleProp =\n      typeof style === 'function' ? style({ pressed: pressedState }) : style;\n\n    const childrenProp =\n      typeof children === 'function'\n        ? children({ pressed: pressedState })\n        : children;\n\n    const rippleColor = useMemo(() => {\n      if (IS_FABRIC === null) {\n        IS_FABRIC = isFabric();\n      }\n\n      const defaultRippleColor = android_ripple ? undefined : 'transparent';\n      const unprocessedRippleColor =\n        android_ripple?.color ?? defaultRippleColor;\n      return IS_FABRIC\n        ? unprocessedRippleColor\n        : processColor(unprocessedRippleColor);\n    }, [android_ripple]);\n\n    return (\n      <GestureDetector gesture={gesture}>\n        <NativeButton\n          {...remainingProps}\n          ref={pressableRef ?? innerPressableRef}\n          accessible={accessible !== false}\n          hitSlop={appliedHitSlop}\n          enabled={isPressableEnabled}\n          touchSoundDisabled={android_disableSound ?? undefined}\n          rippleColor={rippleColor}\n          rippleRadius={android_ripple?.radius ?? undefined}\n          style={[pointerStyle, styleProp]}\n          testOnly_onPress={IS_TEST_ENV ? onPress : undefined}\n          testOnly_onPressIn={IS_TEST_ENV ? onPressIn : undefined}\n          testOnly_onPressOut={IS_TEST_ENV ? onPressOut : undefined}\n          testOnly_onLongPress={IS_TEST_ENV ? onLongPress : undefined}>\n          {childrenProp}\n          {__DEV__ ? (\n            <PressabilityDebugView color=\"red\" hitSlop={normalizedHitSlop} />\n          ) : null}\n        </NativeButton>\n      </GestureDetector>\n    );\n  }\n);\n\nexport default Pressable;\n"]}