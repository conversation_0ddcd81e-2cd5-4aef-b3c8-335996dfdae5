const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { validate, schemas } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');

// Routes publiques
router.post('/register/client', 
  validate(schemas.clientRegister), 
  authController.registerClient
);

router.post('/login/client', 
  validate(schemas.login), 
  authController.loginClient
);

router.post('/login/admin', 
  validate(schemas.login), 
  authController.loginAdmin
);

// Routes protégées (nécessitent une authentification)
router.use(authenticate);

router.get('/profile', authController.getProfile);

router.put('/fcm-token', authController.updateFCMToken);

router.put('/change-password', authController.changePassword);

router.post('/logout', authController.logout);

module.exports = router;
