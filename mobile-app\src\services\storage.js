import { Platform } from 'react-native';
import { STORAGE_KEYS } from '../constants';

// Import conditionnel d'AsyncStorage
let AsyncStorage;
if (Platform.OS === 'web') {
  // Pour le web, utiliser localStorage
  AsyncStorage = {
    getItem: async (key) => {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        return null;
      }
    },
    setItem: async (key, value) => {
      try {
        localStorage.setItem(key, value);
        return true;
      } catch (error) {
        return false;
      }
    },
    removeItem: async (key) => {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        return false;
      }
    },
    multiGet: async (keys) => {
      try {
        return keys.map(key => [key, localStorage.getItem(key)]);
      } catch (error) {
        return keys.map(key => [key, null]);
      }
    },
    multiSet: async (keyValuePairs) => {
      try {
        keyValuePairs.forEach(([key, value]) => {
          localStorage.setItem(key, value);
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    multiRemove: async (keys) => {
      try {
        keys.forEach(key => localStorage.removeItem(key));
        return true;
      } catch (error) {
        return false;
      }
    },
    clear: async () => {
      try {
        localStorage.clear();
        return true;
      } catch (error) {
        return false;
      }
    },
    getAllKeys: async () => {
      try {
        return Object.keys(localStorage);
      } catch (error) {
        return [];
      }
    }
  };
} else {
  // Pour mobile, utiliser AsyncStorage normal
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
}

class StorageService {
  
  // Méthodes génériques pour le stockage
  async setItem(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      return false;
    }
  }

  async getItem(key) {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Erreur lors de la récupération:', error);
      return null;
    }
  }

  async removeItem(key) {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      return false;
    }
  }

  async clear() {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error);
      return false;
    }
  }

  // Méthodes spécifiques pour l'authentification
  async saveAuthData(token, refreshToken, userData, userType) {
    try {
      await AsyncStorage.multiSet([
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.REFRESH_TOKEN, refreshToken],
        [STORAGE_KEYS.USER_DATA, JSON.stringify(userData)],
        [STORAGE_KEYS.USER_TYPE, userType],
      ]);
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données d\'authentification:', error);
      return false;
    }
  }

  async getAuthData() {
    try {
      const values = await AsyncStorage.multiGet([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.USER_TYPE,
      ]);

      const authData = {};
      values.forEach(([key, value]) => {
        if (value) {
          switch (key) {
            case STORAGE_KEYS.AUTH_TOKEN:
              authData.token = value;
              break;
            case STORAGE_KEYS.REFRESH_TOKEN:
              authData.refreshToken = value;
              break;
            case STORAGE_KEYS.USER_DATA:
              authData.userData = JSON.parse(value);
              break;
            case STORAGE_KEYS.USER_TYPE:
              authData.userType = value;
              break;
          }
        }
      });

      return authData;
    } catch (error) {
      console.error('Erreur lors de la récupération des données d\'authentification:', error);
      return {};
    }
  }

  async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.USER_TYPE,
      ]);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression des données d\'authentification:', error);
      return false;
    }
  }

  async isAuthenticated() {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      return !!token;
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'authentification:', error);
      return false;
    }
  }

  // Méthodes pour le panier
  async saveCartData(cartItems) {
    return this.setItem(STORAGE_KEYS.CART_DATA, cartItems);
  }

  async getCartData() {
    const cartData = await this.getItem(STORAGE_KEYS.CART_DATA);
    return cartData || [];
  }

  async clearCartData() {
    return this.removeItem(STORAGE_KEYS.CART_DATA);
  }

  async addToCart(product, quantity = 1) {
    try {
      const cartItems = await this.getCartData();
      const existingItemIndex = cartItems.findIndex(item => item.id === product.id);

      if (existingItemIndex >= 0) {
        // Mettre à jour la quantité si le produit existe déjà
        cartItems[existingItemIndex].quantity += quantity;
      } else {
        // Ajouter un nouveau produit au panier
        cartItems.push({
          id: product.id,
          nom: product.nom,
          prix: product.prix,
          image: product.image,
          unite: product.unite,
          quantity,
          dateAjout: new Date().toISOString(),
        });
      }

      await this.saveCartData(cartItems);
      return cartItems;
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      return null;
    }
  }

  async removeFromCart(productId) {
    try {
      const cartItems = await this.getCartData();
      const updatedCart = cartItems.filter(item => item.id !== productId);
      await this.saveCartData(updatedCart);
      return updatedCart;
    } catch (error) {
      console.error('Erreur lors de la suppression du panier:', error);
      return null;
    }
  }

  async updateCartItemQuantity(productId, quantity) {
    try {
      const cartItems = await this.getCartData();
      const itemIndex = cartItems.findIndex(item => item.id === productId);

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          // Supprimer l'article si la quantité est 0 ou négative
          cartItems.splice(itemIndex, 1);
        } else {
          // Mettre à jour la quantité
          cartItems[itemIndex].quantity = quantity;
        }
        await this.saveCartData(cartItems);
      }

      return cartItems;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la quantité:', error);
      return null;
    }
  }

  async getCartItemCount() {
    try {
      const cartItems = await this.getCartData();
      return cartItems.reduce((total, item) => total + item.quantity, 0);
    } catch (error) {
      console.error('Erreur lors du calcul du nombre d\'articles:', error);
      return 0;
    }
  }

  async getCartTotal() {
    try {
      const cartItems = await this.getCartData();
      return cartItems.reduce((total, item) => total + (item.prix * item.quantity), 0);
    } catch (error) {
      console.error('Erreur lors du calcul du total du panier:', error);
      return 0;
    }
  }

  // Méthodes pour les paramètres
  async saveSettings(settings) {
    return this.setItem(STORAGE_KEYS.SETTINGS, settings);
  }

  async getSettings() {
    const settings = await this.getItem(STORAGE_KEYS.SETTINGS);
    return settings || {
      notifications: true,
      language: 'fr',
      theme: 'light',
    };
  }

  async updateSetting(key, value) {
    try {
      const settings = await this.getSettings();
      settings[key] = value;
      await this.saveSettings(settings);
      return settings;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des paramètres:', error);
      return null;
    }
  }

  // Méthodes pour les données hors ligne
  async saveOfflineData(key, data) {
    const offlineKey = `offline_${key}`;
    return this.setItem(offlineKey, {
      data,
      timestamp: new Date().toISOString(),
    });
  }

  async getOfflineData(key, maxAge = 24 * 60 * 60 * 1000) { // 24 heures par défaut
    try {
      const offlineKey = `offline_${key}`;
      const offlineData = await this.getItem(offlineKey);
      
      if (!offlineData) return null;

      const age = Date.now() - new Date(offlineData.timestamp).getTime();
      if (age > maxAge) {
        // Données trop anciennes, les supprimer
        await this.removeItem(offlineKey);
        return null;
      }

      return offlineData.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des données hors ligne:', error);
      return null;
    }
  }

  async clearOfflineData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const offlineKeys = keys.filter(key => key.startsWith('offline_'));
      await AsyncStorage.multiRemove(offlineKeys);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression des données hors ligne:', error);
      return false;
    }
  }
}

// Instance singleton du service de stockage
const storageService = new StorageService();

export default storageService;
