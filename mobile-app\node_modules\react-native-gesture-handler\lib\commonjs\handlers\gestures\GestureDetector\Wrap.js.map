{"version": 3, "sources": ["Wrap.tsx"], "names": ["Wrap", "React", "Component", "render", "child", "Children", "only", "props", "children", "cloneElement", "collapsable", "e", "Error", "AnimatedWrap", "Reanimated", "default", "createAnimatedComponent"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;AAEO,MAAMA,IAAN,SAAmBC,eAAMC,SAAzB,CAIJ;AACDC,EAAAA,MAAM,GAAG;AACP,QAAI;AACF;AACA;AACA;AACA;AACA;AACA,YAAMC,KAAU,GAAGH,eAAMI,QAAN,CAAeC,IAAf,CAAoB,KAAKC,KAAL,CAAWC,QAA/B,CAAnB;;AACA,0BAAOP,eAAMQ,YAAN,CACLL,KADK,EAEL;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAFK,EAGL;AACAN,MAAAA,KAAK,CAACG,KAAN,CAAYC,QAJP,CAAP;AAMD,KAbD,CAaE,OAAOG,CAAP,EAAU;AACV,YAAM,IAAIC,KAAJ,CACJ,uBACG,2KADH,CADI,CAAN;AAKD;AACF;;AAtBA;;;AAyBI,MAAMC,YAAY,4BACvBC,6BADuB,aACvBA,6BADuB,8CACvBA,8BAAYC,OADW,wDACvB,oBAAqBC,uBAArB,CAA6ChB,IAA7C,CADuB,yEAC+BA,IADjD", "sourcesContent": ["import React from 'react';\nimport { Reanimated } from '../reanimated<PERSON><PERSON>per';\nimport { tagMessage } from '../../../utils';\n\nexport class Wrap extends React.Component<{\n  onGestureHandlerEvent?: unknown;\n  // Implicit `children` prop has been removed in @types/react^18.0.0\n  children?: React.ReactNode;\n}> {\n  render() {\n    try {\n      // I don't think that fighting with types over such a simple function is worth it\n      // The only thing it does is add 'collapsable: false' to the child component\n      // to make sure it is in the native view hierarchy so the detector can find\n      // correct viewTag to attach to.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child: any = React.Children.only(this.props.children);\n      return React.cloneElement(\n        child,\n        { collapsable: false },\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        child.props.children\n      );\n    } catch (e) {\n      throw new Error(\n        tagMessage(\n          `GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n        )\n      );\n    }\n  }\n}\n\nexport const AnimatedWrap =\n  Reanimated?.default?.createAnimatedComponent(Wrap) ?? Wrap;\n"]}