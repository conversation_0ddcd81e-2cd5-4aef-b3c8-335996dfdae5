const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { validateQuery } = require('../middleware/validation');
const { schemas } = require('../middleware/validation');

// Routes publiques (accessibles sans authentification)

// Obtenir tous les produits avec filtres et pagination
router.get('/', 
  validateQuery(schemas.productSearch), 
  productController.getProducts
);

// Obtenir un produit par ID
router.get('/:id', productController.getProductById);

// Obtenir les catégories disponibles
router.get('/meta/categories', productController.getCategories);

// Obtenir les produits en promotion
router.get('/special/promotions', productController.getPromotionalProducts);

// Recherche de produits
router.get('/search/query', productController.searchProducts);

// Obtenir des produits similaires
router.get('/:id/similar', productController.getSimilarProducts);

// Vérifier la disponibilité d'un produit
router.get('/:id/availability', productController.checkAvailability);

module.exports = router;
