const Order = require('../models/Order');
const Product = require('../models/Product');
const Client = require('../models/Client');
const Admin = require('../models/Admin');
const stockService = require('../services/stockService');
const notificationService = require('../services/notificationService');

class OrderController {

  // Créer une nouvelle commande (client)
  async createOrder(req, res) {
    try {
      const { produits, adresseLivraison, notes } = req.body;
      const clientId = req.user._id;

      // Vérifier et préparer les produits
      const produitsDetails = [];
      let montantTotal = 0;

      for (const item of produits) {
        const produit = await Product.findById(item.produitId);
        
        if (!produit || !produit.isActive) {
          return res.status(400).json({
            message: `Produit ${item.produitId} non disponible`
          });
        }

        if (produit.stock < item.quantite) {
          return res.status(400).json({
            message: `Stock insuffisant pour ${produit.nom}. Stock disponible: ${produit.stock}`
          });
        }

        const prixUnitaire = produit.getPrixAvecPromotion();
        const sousTotal = prixUnitaire * item.quantite;

        produitsDetails.push({
          produitId: produit._id,
          nom: produit.nom,
          prix: prixUnitaire,
          quantite: item.quantite,
          unite: produit.unite,
          sousTotal
        });

        montantTotal += sousTotal;
      }

      // Calculer les frais de livraison (exemple: gratuit au-dessus de 50€)
      const fraisLivraison = montantTotal >= 50 ? 0 : 5;
      montantTotal += fraisLivraison;

      // Créer la commande
      const order = new Order({
        clientId,
        produits: produitsDetails,
        adresseLivraison,
        montantTotal,
        fraisLivraison,
        notes,
        dateLivraisonPrevue: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // +2 jours
      });

      await order.save();

      // Réduire le stock
      const stockResult = await stockService.verifierEtReduireStock(
        produits, 
        order._id
      );

      if (!stockResult.success) {
        // Supprimer la commande si le stock n'a pas pu être réduit
        await Order.findByIdAndDelete(order._id);
        return res.status(400).json({
          message: 'Erreur lors de la réservation du stock',
          details: stockResult.produitsIndisponibles
        });
      }

      // Notifier les admins de la nouvelle commande
      const admins = await Admin.find({ isActive: true });
      if (admins.length > 0) {
        await notificationService.notifyNewOrder(order, admins);
      }

      // Populer les détails pour la réponse
      const orderWithDetails = await Order.findById(order._id)
        .populate('clientId', 'nom email telephone');

      res.status(201).json({
        message: 'Commande créée avec succès',
        order: orderWithDetails,
        stockUpdates: stockResult.stockUpdates
      });

    } catch (error) {
      console.error('Erreur création commande:', error);
      res.status(500).json({
        message: 'Erreur lors de la création de la commande',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir les commandes du client connecté
  async getClientOrders(req, res) {
    try {
      const clientId = req.user._id;
      const { page = 1, limit = 10, statut } = req.query;
      const skip = (page - 1) * limit;

      // Construction de la requête
      const query = { clientId };
      if (statut) {
        query.statut = statut;
      }

      const [orders, total] = await Promise.all([
        Order.find(query)
          .sort({ dateCommande: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .populate('produits.produitId', 'nom image categorie'),
        Order.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });

    } catch (error) {
      console.error('Erreur récupération commandes client:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des commandes',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir une commande spécifique du client
  async getClientOrderById(req, res) {
    try {
      const { id } = req.params;
      const clientId = req.user._id;

      const order = await Order.findOne({ _id: id, clientId })
        .populate('produits.produitId', 'nom image categorie unite')
        .populate('historiqueStatut.modifiePar', 'nom');

      if (!order) {
        return res.status(404).json({
          message: 'Commande non trouvée'
        });
      }

      res.json(order);

    } catch (error) {
      console.error('Erreur récupération commande:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération de la commande',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Annuler une commande (client)
  async cancelOrder(req, res) {
    try {
      const { id } = req.params;
      const clientId = req.user._id;
      const { raison } = req.body;

      const order = await Order.findOne({ _id: id, clientId });

      if (!order) {
        return res.status(404).json({
          message: 'Commande non trouvée'
        });
      }

      if (!order.peutEtreAnnulee()) {
        return res.status(400).json({
          message: 'Cette commande ne peut plus être annulée',
          statutActuel: order.statut
        });
      }

      // Restaurer le stock
      await stockService.restaurerStock(
        order.produits,
        order._id,
        raison || 'Annulation par le client'
      );

      // Mettre à jour le statut
      await order.changerStatut('annulee', raison || 'Annulée par le client');

      res.json({
        message: 'Commande annulée avec succès',
        order
      });

    } catch (error) {
      console.error('Erreur annulation commande:', error);
      res.status(500).json({
        message: 'Erreur lors de l\'annulation de la commande',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir le statut de livraison d'une commande
  async getOrderStatus(req, res) {
    try {
      const { id } = req.params;
      const clientId = req.user._id;

      const order = await Order.findOne({ _id: id, clientId })
        .select('numeroCommande statut dateCommande dateLivraisonPrevue dateLivraisonReelle historiqueStatut livreur')
        .populate('historiqueStatut.modifiePar', 'nom');

      if (!order) {
        return res.status(404).json({
          message: 'Commande non trouvée'
        });
      }

      // Calculer le délai estimé
      let delaiEstime = null;
      if (order.dateLivraisonPrevue && order.statut !== 'livree') {
        const maintenant = new Date();
        const diffMs = order.dateLivraisonPrevue - maintenant;
        delaiEstime = Math.ceil(diffMs / (1000 * 60 * 60 * 24)); // en jours
      }

      res.json({
        numeroCommande: order.numeroCommande,
        statut: order.statut,
        statutLivraison: order.statutLivraison,
        dateCommande: order.dateCommande,
        dateLivraisonPrevue: order.dateLivraisonPrevue,
        dateLivraisonReelle: order.dateLivraisonReelle,
        delaiEstime,
        delaiLivraison: order.delaiLivraison,
        livreur: order.livreur,
        historiqueStatut: order.historiqueStatut
      });

    } catch (error) {
      console.error('Erreur récupération statut commande:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération du statut',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir les statistiques des commandes du client
  async getClientOrderStats(req, res) {
    try {
      const clientId = req.user._id;

      const stats = await Order.aggregate([
        { $match: { clientId } },
        {
          $group: {
            _id: null,
            totalCommandes: { $sum: 1 },
            montantTotal: { $sum: '$montantTotal' },
            commandesLivrees: {
              $sum: { $cond: [{ $eq: ['$statut', 'livree'] }, 1, 0] }
            },
            commandesEnCours: {
              $sum: { 
                $cond: [
                  { $in: ['$statut', ['en_attente', 'confirmee', 'en_preparation', 'en_livraison']] }, 
                  1, 
                  0
                ] 
              }
            },
            commandesAnnulees: {
              $sum: { $cond: [{ $eq: ['$statut', 'annulee'] }, 1, 0] }
            }
          }
        }
      ]);

      const clientStats = stats[0] || {
        totalCommandes: 0,
        montantTotal: 0,
        commandesLivrees: 0,
        commandesEnCours: 0,
        commandesAnnulees: 0
      };

      // Commande la plus récente
      const derniereCommande = await Order.findOne({ clientId })
        .sort({ dateCommande: -1 })
        .select('numeroCommande statut dateCommande montantTotal');

      res.json({
        ...clientStats,
        panierMoyen: clientStats.totalCommandes > 0 
          ? (clientStats.montantTotal / clientStats.totalCommandes).toFixed(2)
          : 0,
        derniereCommande
      });

    } catch (error) {
      console.error('Erreur récupération statistiques client:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des statistiques',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
}

module.exports = new OrderController();
