{"version": 3, "sources": ["GestureHandlerOrchestrator.ts"], "names": ["PointerType", "State", "PointerTracker", "GestureHandlerOrchestrator", "constructor", "Set", "scheduleFinishedHandlersCleanup", "handlingChangeSemaphore", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "active", "awaiting", "activationIndex", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "indexInGestureHandlers", "gestureHandlers", "indexOf", "indexInAwaitingHandlers", "awaitingHandlers", "splice", "awaitingHandlersTags", "delete", "handlerTag", "handlersToRemove", "i", "length", "isFinished", "state", "add", "filter", "has", "hasOtherHandlerToWaitFor", "hasToWaitFor", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "some", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "END", "tryActivate", "cancel", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlerState", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "shouldBeCancelledBy", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "enabled", "sendEvent", "UNDETERMINED", "includes", "currentState", "shouldResetProgress", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "shareCommonPointers", "delegate", "view", "checkOverlap", "isPointerWithinBothBounds", "pointer", "point", "tracker", "getLastAbsoluteCoords", "isPointerInBounds", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "pointerType", "MOUSE", "STYLUS", "resetTracker", "instance", "_instance"], "mappings": ";;AAAA,SAASA,WAAT,QAA4B,mBAA5B;AACA,SAASC,KAAT,QAAsB,aAAtB;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,0BAAN,CAAiC;AAU9C;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,6CATuB,EASvB;;AAAA,8CARwB,EAQxB;;AAAA,kDAPsB,IAAIC,GAAJ,EAOtB;;AAAA,qDALY,CAKZ;;AAAA,6CAJI,CAIJ;AAAE;;AAEhBC,EAAAA,+BAA+B,GAAS;AAC9C,QAAI,KAAKC,uBAAL,KAAiC,CAArC,EAAwC;AACtC,WAAKC,uBAAL;AACD;AACF;;AAEOC,EAAAA,YAAY,CAACC,OAAD,EAAiC;AACnDA,IAAAA,OAAO,CAACC,KAAR;AACAD,IAAAA,OAAO,CAACE,MAAR,GAAiB,KAAjB;AACAF,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0BC,MAAM,CAACC,SAAjC;AACD;;AAEMC,EAAAA,6BAA6B,CAACP,OAAD,EAAiC;AACnE,UAAMQ,sBAAsB,GAAG,KAAKC,eAAL,CAAqBC,OAArB,CAA6BV,OAA7B,CAA/B;AACA,UAAMW,uBAAuB,GAAG,KAAKC,gBAAL,CAAsBF,OAAtB,CAA8BV,OAA9B,CAAhC;;AAEA,QAAIQ,sBAAsB,IAAI,CAA9B,EAAiC;AAC/B,WAAKC,eAAL,CAAqBI,MAArB,CAA4BL,sBAA5B,EAAoD,CAApD;AACD;;AAED,QAAIG,uBAAuB,IAAI,CAA/B,EAAkC;AAChC,WAAKC,gBAAL,CAAsBC,MAAtB,CAA6BF,uBAA7B,EAAsD,CAAtD;AACA,WAAKG,oBAAL,CAA0BC,MAA1B,CAAiCf,OAAO,CAACgB,UAAzC;AACD;AACF;;AAEOlB,EAAAA,uBAAuB,GAAS;AACtC,UAAMmB,gBAAgB,GAAG,IAAItB,GAAJ,EAAzB;;AAEA,SAAK,IAAIuB,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,YAAMlB,OAAO,GAAG,KAAKS,eAAL,CAAqBS,CAArB,CAAhB;;AAEA,UAAI,KAAKE,UAAL,CAAgBpB,OAAO,CAACqB,KAAxB,KAAkC,CAACrB,OAAO,CAACG,QAA/C,EAAyD;AACvD,aAAKJ,YAAL,CAAkBC,OAAlB;AACAiB,QAAAA,gBAAgB,CAACK,GAAjB,CAAqBtB,OAArB;AACD;AACF;;AAED,SAAKS,eAAL,GAAuB,KAAKA,eAAL,CAAqBc,MAArB,CACpBvB,OAAD,IAAa,CAACiB,gBAAgB,CAACO,GAAjB,CAAqBxB,OAArB,CADO,CAAvB;AAGD;;AAEOyB,EAAAA,wBAAwB,CAACzB,OAAD,EAAoC;AAClE,UAAM0B,YAAY,GAAIC,YAAD,IAAmC;AACtD,aACE,CAAC,KAAKP,UAAL,CAAgBO,YAAY,CAACN,KAA7B,CAAD,IACA,KAAKO,yBAAL,CAA+B5B,OAA/B,EAAwC2B,YAAxC,CAFF;AAID,KALD;;AAOA,WAAO,KAAKlB,eAAL,CAAqBoB,IAArB,CAA0BH,YAA1B,CAAP;AACD;;AAEOI,EAAAA,kCAAkC,CACxC9B,OADwC,EAE/B;AACT,UAAM+B,iBAAiB,GAAIJ,YAAD,IAAmC;AAC3D,aACE,KAAKC,yBAAL,CAA+B5B,OAA/B,EAAwC2B,YAAxC,KACAA,YAAY,CAACN,KAAb,KAAuB9B,KAAK,CAACyC,GAF/B;AAID,KALD;;AAOA,WAAO,KAAKvB,eAAL,CAAqBoB,IAArB,CAA0BE,iBAA1B,CAAP;AACD;;AAEOE,EAAAA,WAAW,CAACjC,OAAD,EAAiC;AAClD,QAAI,KAAK8B,kCAAL,CAAwC9B,OAAxC,CAAJ,EAAsD;AACpDA,MAAAA,OAAO,CAACkC,MAAR;AACA;AACD;;AAED,QAAI,KAAKT,wBAAL,CAA8BzB,OAA9B,CAAJ,EAA4C;AAC1C,WAAKmC,kBAAL,CAAwBnC,OAAxB;AACA;AACD;;AAED,UAAMoC,YAAY,GAAGpC,OAAO,CAACqB,KAA7B;;AAEA,QAAIe,YAAY,KAAK7C,KAAK,CAAC8C,SAAvB,IAAoCD,YAAY,KAAK7C,KAAK,CAAC+C,MAA/D,EAAuE;AACrE;AACD;;AAED,QAAI,KAAKC,cAAL,CAAoBvC,OAApB,CAAJ,EAAkC;AAChC,WAAKwC,UAAL,CAAgBxC,OAAhB;AACA;AACD;;AAED,QAAIoC,YAAY,KAAK7C,KAAK,CAACkD,MAA3B,EAAmC;AACjCzC,MAAAA,OAAO,CAAC0C,IAAR;AACA;AACD;;AAED,QAAIN,YAAY,KAAK7C,KAAK,CAACoD,KAA3B,EAAkC;AAChC3C,MAAAA,OAAO,CAACkC,MAAR;AACD;AACF;;AAEOK,EAAAA,cAAc,CAACvC,OAAD,EAAoC;AACxD,UAAM4C,mBAAmB,GAAIjB,YAAD,IAAmC;AAC7D,aAAO,KAAKkB,0BAAL,CAAgC7C,OAAhC,EAAyC2B,YAAzC,CAAP;AACD,KAFD;;AAIA,WAAO,CAAC,KAAKlB,eAAL,CAAqBoB,IAArB,CAA0Be,mBAA1B,CAAR;AACD;;AAEOE,EAAAA,uBAAuB,CAAC9C,OAAD,EAAiC;AAC9D,UAAM+C,UAAU,GAAIpB,YAAD,IAAmC;AACpD,aACE,CAACA,YAAY,CAACxB,QAAd,IACA,KAAKyB,yBAAL,CAA+BD,YAA/B,EAA6C3B,OAA7C,CAFF;AAID,KALD;;AAOA,SAAK,MAAM2B,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,UAAImC,UAAU,CAACpB,YAAD,CAAd,EAA8B;AAC5B,aAAK5B,YAAL,CAAkB4B,YAAlB;AACA,aAAKb,oBAAL,CAA0BC,MAA1B,CAAiCY,YAAY,CAACX,UAA9C;AACD;AACF;;AAED,SAAKJ,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBW,MAAtB,CAA8BI,YAAD,IACnD,KAAKb,oBAAL,CAA0BU,GAA1B,CAA8BG,YAAY,CAACX,UAA3C,CADsB,CAAxB;AAGD;;AAEMgC,EAAAA,oBAAoB,CACzBhD,OADyB,EAEzBiD,QAFyB,EAGzBC,QAHyB,EAIzBC,cAJyB,EAKnB;AACN,QAAI,CAACnD,OAAO,CAACoD,OAAT,IAAoB,CAACD,cAAzB,EAAyC;AACvC;AACD;;AAED,SAAKtD,uBAAL,IAAgC,CAAhC;;AAEA,QAAI,KAAKuB,UAAL,CAAgB6B,QAAhB,CAAJ,EAA+B;AAC7B,WAAK,MAAMtB,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,YACE,CAAC,KAAKgB,yBAAL,CAA+BD,YAA/B,EAA6C3B,OAA7C,CAAD,IACA,CAAC,KAAKc,oBAAL,CAA0BU,GAA1B,CAA8BG,YAAY,CAACX,UAA3C,CAFH,EAGE;AACA;AACD;;AAED,YAAIiC,QAAQ,KAAK1D,KAAK,CAACyC,GAAvB,EAA4B;AAC1B,eAAKC,WAAL,CAAiBN,YAAjB;AACA;AACD;;AAEDA,QAAAA,YAAY,CAACO,MAAb;;AAEA,YAAIP,YAAY,CAACN,KAAb,KAAuB9B,KAAK,CAACyC,GAAjC,EAAsC;AACpC;AACA;AACA;AACA;AACAL,UAAAA,YAAY,CAAC0B,SAAb,CAAuB9D,KAAK,CAAC8C,SAA7B,EAAwC9C,KAAK,CAACoD,KAA9C;AACD;;AAEDhB,QAAAA,YAAY,CAACxB,QAAb,GAAwB,KAAxB;AACD;AACF;;AAED,QAAI8C,QAAQ,KAAK1D,KAAK,CAACkD,MAAvB,EAA+B;AAC7B,WAAKR,WAAL,CAAiBjC,OAAjB;AACD,KAFD,MAEO,IAAIkD,QAAQ,KAAK3D,KAAK,CAACkD,MAAnB,IAA6BS,QAAQ,KAAK3D,KAAK,CAACyC,GAApD,EAAyD;AAC9D,UAAIhC,OAAO,CAACE,MAAZ,EAAoB;AAClBF,QAAAA,OAAO,CAACqD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD,OAFD,MAEO,IACLA,QAAQ,KAAK3D,KAAK,CAACkD,MAAnB,KACCQ,QAAQ,KAAK1D,KAAK,CAAC8C,SAAnB,IAAgCY,QAAQ,KAAK1D,KAAK,CAAC+C,MADpD,CADK,EAGL;AACAtC,QAAAA,OAAO,CAACqD,SAAR,CAAkBJ,QAAlB,EAA4B1D,KAAK,CAACoD,KAAlC;AACD;AACF,KATM,MASA,IACLO,QAAQ,KAAK3D,KAAK,CAAC+D,YAAnB,IACAL,QAAQ,KAAK1D,KAAK,CAAC8C,SAFd,EAGL;AACArC,MAAAA,OAAO,CAACqD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD;;AAED,SAAKrD,uBAAL,IAAgC,CAAhC;AAEA,SAAKD,+BAAL;;AAEA,QAAI,CAAC,KAAKgB,gBAAL,CAAsB2C,QAAtB,CAA+BvD,OAA/B,CAAL,EAA8C;AAC5C,WAAK8C,uBAAL,CAA6B9C,OAA7B;AACD;AACF;;AAEOwC,EAAAA,UAAU,CAACxC,OAAD,EAAiC;AACjD,UAAMwD,YAAY,GAAGxD,OAAO,CAACqB,KAA7B;AAEArB,IAAAA,OAAO,CAACE,MAAR,GAAiB,IAAjB;AACAF,IAAAA,OAAO,CAACyD,mBAAR,GAA8B,IAA9B;AACAzD,IAAAA,OAAO,CAACI,eAAR,GAA0B,KAAKA,eAAL,EAA1B;;AAEA,SAAK,IAAIc,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,UAAI,KAAK2B,0BAAL,CAAgC,KAAKpC,eAAL,CAAqBS,CAArB,CAAhC,EAAyDlB,OAAzD,CAAJ,EAAuE;AACrE,aAAKS,eAAL,CAAqBS,CAArB,EAAwBgB,MAAxB;AACD;AACF;;AAED,SAAK,MAAMP,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,UAAI,KAAKiC,0BAAL,CAAgClB,YAAhC,EAA8C3B,OAA9C,CAAJ,EAA4D;AAC1D2B,QAAAA,YAAY,CAACxB,QAAb,GAAwB,KAAxB;AACD;AACF;;AAEDH,IAAAA,OAAO,CAACqD,SAAR,CAAkB9D,KAAK,CAACkD,MAAxB,EAAgClD,KAAK,CAACoD,KAAtC;;AAEA,QAAIa,YAAY,KAAKjE,KAAK,CAACkD,MAA3B,EAAmC;AACjCzC,MAAAA,OAAO,CAACqD,SAAR,CAAkB9D,KAAK,CAACyC,GAAxB,EAA6BzC,KAAK,CAACkD,MAAnC;;AACA,UAAIe,YAAY,KAAKjE,KAAK,CAACyC,GAA3B,EAAgC;AAC9BhC,QAAAA,OAAO,CAACqD,SAAR,CAAkB9D,KAAK,CAAC+D,YAAxB,EAAsC/D,KAAK,CAACyC,GAA5C;AACD;AACF;;AAED,QAAI,CAAChC,OAAO,CAACG,QAAb,EAAuB;AACrB;AACD;;AAEDH,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AAEA,SAAKS,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBW,MAAtB,CACrBI,YAAD,IAAkBA,YAAY,KAAK3B,OADb,CAAxB;AAGD;;AAEOmC,EAAAA,kBAAkB,CAACnC,OAAD,EAAiC;AACzD,QAAI,KAAKY,gBAAL,CAAsB2C,QAAtB,CAA+BvD,OAA/B,CAAJ,EAA6C;AAC3C;AACD;;AAED,SAAKY,gBAAL,CAAsB8C,IAAtB,CAA2B1D,OAA3B;AACA,SAAKc,oBAAL,CAA0BQ,GAA1B,CAA8BtB,OAAO,CAACgB,UAAtC;AAEAhB,IAAAA,OAAO,CAACG,QAAR,GAAmB,IAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0B,KAAKA,eAAL,EAA1B;AACD;;AAEMuD,EAAAA,yBAAyB,CAAC3D,OAAD,EAAiC;AAC/D,QAAI,KAAKS,eAAL,CAAqB8C,QAArB,CAA8BvD,OAA9B,CAAJ,EAA4C;AAC1C;AACD;;AAED,SAAKS,eAAL,CAAqBiD,IAArB,CAA0B1D,OAA1B;AAEAA,IAAAA,OAAO,CAACE,MAAR,GAAiB,KAAjB;AACAF,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0BC,MAAM,CAACuD,gBAAjC;AACD;;AAEOhC,EAAAA,yBAAyB,CAC/B5B,OAD+B,EAE/B2B,YAF+B,EAGtB;AACT,WACE3B,OAAO,KAAK2B,YAAZ,KACC3B,OAAO,CAAC6D,2BAAR,CAAoClC,YAApC,KACCA,YAAY,CAACmC,6BAAb,CAA2C9D,OAA3C,CAFF,CADF;AAKD;;AAEO+D,EAAAA,oBAAoB,CAC1BC,GAD0B,EAE1BC,GAF0B,EAGjB;AACT,WACED,GAAG,KAAKC,GAAR,IACAD,GAAG,CAACE,6BAAJ,CAAkCD,GAAlC,CADA,IAEAA,GAAG,CAACC,6BAAJ,CAAkCF,GAAlC,CAHF;AAKD;;AAEOnB,EAAAA,0BAA0B,CAChC7C,OADgC,EAEhC2B,YAFgC,EAGvB;AACT,QAAI,KAAKoC,oBAAL,CAA0B/D,OAA1B,EAAmC2B,YAAnC,CAAJ,EAAsD;AACpD,aAAO,KAAP;AACD;;AAED,QAAI3B,OAAO,CAACG,QAAR,IAAoBH,OAAO,CAACqB,KAAR,KAAkB9B,KAAK,CAACkD,MAAhD,EAAwD;AACtD,aAAOzC,OAAO,CAACmE,wBAAR,CAAiCxC,YAAjC,CAAP;AACD;;AAED,UAAMyC,eAAyB,GAAGpE,OAAO,CAACqE,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAG3C,YAAY,CAAC0C,oBAAb,EAAhC;;AAEA,QACE,CAAC7E,cAAc,CAAC+E,mBAAf,CAAmCH,eAAnC,EAAoDE,aAApD,CAAD,IACAtE,OAAO,CAACwE,QAAR,CAAiBC,IAAjB,KAA0B9C,YAAY,CAAC6C,QAAb,CAAsBC,IAFlD,EAGE;AACA,aAAO,KAAKC,YAAL,CAAkB1E,OAAlB,EAA2B2B,YAA3B,CAAP;AACD;;AAED,WAAO,IAAP;AACD;;AAEO+C,EAAAA,YAAY,CAClB1E,OADkB,EAElB2B,YAFkB,EAGT;AACT;AACA;AACA;AAEA;AAEA,UAAMgD,yBAAyB,GAAIC,OAAD,IAAqB;AACrD,YAAMC,KAAK,GAAG7E,OAAO,CAAC8E,OAAR,CAAgBC,qBAAhB,CAAsCH,OAAtC,CAAd;AAEA,aACE5E,OAAO,CAACwE,QAAR,CAAiBQ,iBAAjB,CAAmCH,KAAnC,KACAlD,YAAY,CAAC6C,QAAb,CAAsBQ,iBAAtB,CAAwCH,KAAxC,CAFF;AAID,KAPD;;AASA,WAAO7E,OAAO,CAACqE,oBAAR,GAA+BxC,IAA/B,CAAoC8C,yBAApC,CAAP;AACD;;AAEOvD,EAAAA,UAAU,CAACC,KAAD,EAAwB;AACxC,WACEA,KAAK,KAAK9B,KAAK,CAACyC,GAAhB,IAAuBX,KAAK,KAAK9B,KAAK,CAAC+C,MAAvC,IAAiDjB,KAAK,KAAK9B,KAAK,CAAC8C,SADnE;AAGD,GAzV6C,CA2V9C;AACA;AACA;AACA;AACA;AACA;;;AACO4C,EAAAA,yBAAyB,CAACC,cAAD,EAAwC;AACtE,SAAKzE,eAAL,CAAqB0E,OAArB,CAA8BnF,OAAD,IAA8B;AACzD,UACEA,OAAO,CAACoF,WAAR,KAAwB9F,WAAW,CAAC+F,KAApC,IACArF,OAAO,CAACoF,WAAR,KAAwB9F,WAAW,CAACgG,MAFtC,EAGE;AACA;AACD;;AAED,UAAItF,OAAO,KAAKkF,cAAhB,EAAgC;AAC9BlF,QAAAA,OAAO,CAACkC,MAAR;AACD,OAFD,MAEO;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,QAAAA,OAAO,CAAC8E,OAAR,CAAgBS,YAAhB;AACD;AACF,KApBD;AAqBD;;AAEyB,aAARC,QAAQ,GAA+B;AACvD,QAAI,CAAC/F,0BAA0B,CAACgG,SAAhC,EAA2C;AACzChG,MAAAA,0BAA0B,CAACgG,SAA3B,GAAuC,IAAIhG,0BAAJ,EAAvC;AACD;;AAED,WAAOA,0BAA0B,CAACgG,SAAlC;AACD;;AA/X6C;;gBAA3BhG,0B", "sourcesContent": ["import { PointerType } from '../../PointerType';\nimport { State } from '../../State';\n\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport PointerTracker from './PointerTracker';\n\nexport default class GestureHandlerOrchestrator {\n  private static _instance: GestureHandlerOrchestrator;\n\n  private gestureHandlers: IGestureHandler[] = [];\n  private awaitingHandlers: IGestureHandler[] = [];\n  private awaitingHandlersTags: Set<number> = new Set();\n\n  private handlingChangeSemaphore = 0;\n  private activationIndex = 0;\n\n  // Private beacuse of Singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  private scheduleFinishedHandlersCleanup(): void {\n    if (this.handlingChangeSemaphore === 0) {\n      this.cleanupFinishedHandlers();\n    }\n  }\n\n  private cleanHandler(handler: IGestureHandler): void {\n    handler.reset();\n    handler.active = false;\n    handler.awaiting = false;\n    handler.activationIndex = Number.MAX_VALUE;\n  }\n\n  public removeHandlerFromOrchestrator(handler: IGestureHandler): void {\n    const indexInGestureHandlers = this.gestureHandlers.indexOf(handler);\n    const indexInAwaitingHandlers = this.awaitingHandlers.indexOf(handler);\n\n    if (indexInGestureHandlers >= 0) {\n      this.gestureHandlers.splice(indexInGestureHandlers, 1);\n    }\n\n    if (indexInAwaitingHandlers >= 0) {\n      this.awaitingHandlers.splice(indexInAwaitingHandlers, 1);\n      this.awaitingHandlersTags.delete(handler.handlerTag);\n    }\n  }\n\n  private cleanupFinishedHandlers(): void {\n    const handlersToRemove = new Set<IGestureHandler>();\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      const handler = this.gestureHandlers[i];\n\n      if (this.isFinished(handler.state) && !handler.awaiting) {\n        this.cleanHandler(handler);\n        handlersToRemove.add(handler);\n      }\n    }\n\n    this.gestureHandlers = this.gestureHandlers.filter(\n      (handler) => !handlersToRemove.has(handler)\n    );\n  }\n\n  private hasOtherHandlerToWaitFor(handler: IGestureHandler): boolean {\n    const hasToWaitFor = (otherHandler: IGestureHandler) => {\n      return (\n        !this.isFinished(otherHandler.state) &&\n        this.shouldHandlerWaitForOther(handler, otherHandler)\n      );\n    };\n\n    return this.gestureHandlers.some(hasToWaitFor);\n  }\n\n  private shouldBeCancelledByFinishedHandler(\n    handler: IGestureHandler\n  ): boolean {\n    const shouldBeCancelled = (otherHandler: IGestureHandler) => {\n      return (\n        this.shouldHandlerWaitForOther(handler, otherHandler) &&\n        otherHandler.state === State.END\n      );\n    };\n\n    return this.gestureHandlers.some(shouldBeCancelled);\n  }\n\n  private tryActivate(handler: IGestureHandler): void {\n    if (this.shouldBeCancelledByFinishedHandler(handler)) {\n      handler.cancel();\n      return;\n    }\n\n    if (this.hasOtherHandlerToWaitFor(handler)) {\n      this.addAwaitingHandler(handler);\n      return;\n    }\n\n    const handlerState = handler.state;\n\n    if (handlerState === State.CANCELLED || handlerState === State.FAILED) {\n      return;\n    }\n\n    if (this.shouldActivate(handler)) {\n      this.makeActive(handler);\n      return;\n    }\n\n    if (handlerState === State.ACTIVE) {\n      handler.fail();\n      return;\n    }\n\n    if (handlerState === State.BEGAN) {\n      handler.cancel();\n    }\n  }\n\n  private shouldActivate(handler: IGestureHandler): boolean {\n    const shouldBeCancelledBy = (otherHandler: IGestureHandler) => {\n      return this.shouldHandlerBeCancelledBy(handler, otherHandler);\n    };\n\n    return !this.gestureHandlers.some(shouldBeCancelledBy);\n  }\n\n  private cleanupAwaitingHandlers(handler: IGestureHandler): void {\n    const shouldWait = (otherHandler: IGestureHandler) => {\n      return (\n        !otherHandler.awaiting &&\n        this.shouldHandlerWaitForOther(otherHandler, handler)\n      );\n    };\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (shouldWait(otherHandler)) {\n        this.cleanHandler(otherHandler);\n        this.awaitingHandlersTags.delete(otherHandler.handlerTag);\n      }\n    }\n\n    this.awaitingHandlers = this.awaitingHandlers.filter((otherHandler) =>\n      this.awaitingHandlersTags.has(otherHandler.handlerTag)\n    );\n  }\n\n  public onHandlerStateChange(\n    handler: IGestureHandler,\n    newState: State,\n    oldState: State,\n    sendIfDisabled?: boolean\n  ): void {\n    if (!handler.enabled && !sendIfDisabled) {\n      return;\n    }\n\n    this.handlingChangeSemaphore += 1;\n\n    if (this.isFinished(newState)) {\n      for (const otherHandler of this.awaitingHandlers) {\n        if (\n          !this.shouldHandlerWaitForOther(otherHandler, handler) ||\n          !this.awaitingHandlersTags.has(otherHandler.handlerTag)\n        ) {\n          continue;\n        }\n\n        if (newState !== State.END) {\n          this.tryActivate(otherHandler);\n          continue;\n        }\n\n        otherHandler.cancel();\n\n        if (otherHandler.state === State.END) {\n          // Handle edge case, where discrete gestures end immediately after activation thus\n          // their state is set to END and when the gesture they are waiting for activates they\n          // should be cancelled, however `cancel` was never sent as gestures were already in the END state.\n          // Send synthetic BEGAN -> CANCELLED to properly handle JS logic\n          otherHandler.sendEvent(State.CANCELLED, State.BEGAN);\n        }\n\n        otherHandler.awaiting = false;\n      }\n    }\n\n    if (newState === State.ACTIVE) {\n      this.tryActivate(handler);\n    } else if (oldState === State.ACTIVE || oldState === State.END) {\n      if (handler.active) {\n        handler.sendEvent(newState, oldState);\n      } else if (\n        oldState === State.ACTIVE &&\n        (newState === State.CANCELLED || newState === State.FAILED)\n      ) {\n        handler.sendEvent(newState, State.BEGAN);\n      }\n    } else if (\n      oldState !== State.UNDETERMINED ||\n      newState !== State.CANCELLED\n    ) {\n      handler.sendEvent(newState, oldState);\n    }\n\n    this.handlingChangeSemaphore -= 1;\n\n    this.scheduleFinishedHandlersCleanup();\n\n    if (!this.awaitingHandlers.includes(handler)) {\n      this.cleanupAwaitingHandlers(handler);\n    }\n  }\n\n  private makeActive(handler: IGestureHandler): void {\n    const currentState = handler.state;\n\n    handler.active = true;\n    handler.shouldResetProgress = true;\n    handler.activationIndex = this.activationIndex++;\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      if (this.shouldHandlerBeCancelledBy(this.gestureHandlers[i], handler)) {\n        this.gestureHandlers[i].cancel();\n      }\n    }\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (this.shouldHandlerBeCancelledBy(otherHandler, handler)) {\n        otherHandler.awaiting = false;\n      }\n    }\n\n    handler.sendEvent(State.ACTIVE, State.BEGAN);\n\n    if (currentState !== State.ACTIVE) {\n      handler.sendEvent(State.END, State.ACTIVE);\n      if (currentState !== State.END) {\n        handler.sendEvent(State.UNDETERMINED, State.END);\n      }\n    }\n\n    if (!handler.awaiting) {\n      return;\n    }\n\n    handler.awaiting = false;\n\n    this.awaitingHandlers = this.awaitingHandlers.filter(\n      (otherHandler) => otherHandler !== handler\n    );\n  }\n\n  private addAwaitingHandler(handler: IGestureHandler): void {\n    if (this.awaitingHandlers.includes(handler)) {\n      return;\n    }\n\n    this.awaitingHandlers.push(handler);\n    this.awaitingHandlersTags.add(handler.handlerTag);\n\n    handler.awaiting = true;\n    handler.activationIndex = this.activationIndex++;\n  }\n\n  public recordHandlerIfNotPresent(handler: IGestureHandler): void {\n    if (this.gestureHandlers.includes(handler)) {\n      return;\n    }\n\n    this.gestureHandlers.push(handler);\n\n    handler.active = false;\n    handler.awaiting = false;\n    handler.activationIndex = Number.MAX_SAFE_INTEGER;\n  }\n\n  private shouldHandlerWaitForOther(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    return (\n      handler !== otherHandler &&\n      (handler.shouldWaitForHandlerFailure(otherHandler) ||\n        otherHandler.shouldRequireToWaitForFailure(handler))\n    );\n  }\n\n  private canRunSimultaneously(\n    gh1: IGestureHandler,\n    gh2: IGestureHandler\n  ): boolean {\n    return (\n      gh1 === gh2 ||\n      gh1.shouldRecognizeSimultaneously(gh2) ||\n      gh2.shouldRecognizeSimultaneously(gh1)\n    );\n  }\n\n  private shouldHandlerBeCancelledBy(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    if (this.canRunSimultaneously(handler, otherHandler)) {\n      return false;\n    }\n\n    if (handler.awaiting || handler.state === State.ACTIVE) {\n      return handler.shouldBeCancelledByOther(otherHandler);\n    }\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    if (\n      !PointerTracker.shareCommonPointers(handlerPointers, otherPointers) &&\n      handler.delegate.view !== otherHandler.delegate.view\n    ) {\n      return this.checkOverlap(handler, otherHandler);\n    }\n\n    return true;\n  }\n\n  private checkOverlap(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    // If handlers don't have common pointers, default return value is false.\n    // However, if at least on pointer overlaps with both handlers, we return true\n    // This solves issue in overlapping parents example\n\n    // TODO: Find better way to handle that issue, for example by activation order and handler cancelling\n\n    const isPointerWithinBothBounds = (pointer: number) => {\n      const point = handler.tracker.getLastAbsoluteCoords(pointer);\n\n      return (\n        handler.delegate.isPointerInBounds(point) &&\n        otherHandler.delegate.isPointerInBounds(point)\n      );\n    };\n\n    return handler.getTrackedPointersID().some(isPointerWithinBothBounds);\n  }\n\n  private isFinished(state: State): boolean {\n    return (\n      state === State.END || state === State.FAILED || state === State.CANCELLED\n    );\n  }\n\n  // This function is called when handler receives touchdown event\n  // If handler is using mouse or pen as a pointer and any handler receives touch event,\n  // mouse/pen event dissappears - it doesn't send onPointerCancel nor onPointerUp (and others)\n  // This became a problem because handler was left at active state without any signal to end or fail\n  // To handle this, when new touch event is received, we loop through active handlers and check which type of\n  // pointer they're using. If there are any handler with mouse/pen as a pointer, we cancel them\n  public cancelMouseAndPenGestures(currentHandler: IGestureHandler): void {\n    this.gestureHandlers.forEach((handler: IGestureHandler) => {\n      if (\n        handler.pointerType !== PointerType.MOUSE &&\n        handler.pointerType !== PointerType.STYLUS\n      ) {\n        return;\n      }\n\n      if (handler !== currentHandler) {\n        handler.cancel();\n      } else {\n        // Handler that received touch event should have its pointer tracker reset\n        // This allows handler to smoothly change from mouse/pen to touch\n        // The drawback is, that when we try to use mouse/pen one more time, it doesn't send onPointerDown at the first time\n        // so it is required to click two times to get handler to work\n        //\n        // However, handler will receive manually created onPointerEnter that is triggered in EventManager in onPointerMove method.\n        // There may be possibility to use that fact to make handler respond properly to first mouse click\n        handler.tracker.resetTracker();\n      }\n    });\n  }\n\n  public static get instance(): GestureHandlerOrchestrator {\n    if (!GestureHandlerOrchestrator._instance) {\n      GestureHandlerOrchestrator._instance = new GestureHandlerOrchestrator();\n    }\n\n    return GestureHandlerOrchestrator._instance;\n  }\n}\n"]}