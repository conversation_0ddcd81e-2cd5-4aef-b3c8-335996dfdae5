{"version": 3, "sources": ["FlingGestureHandler.ts"], "names": ["Hammer", "Direction", "GesturePropError", "DraggingGestureHandler", "isnan", "FlingGestureHandler", "name", "NativeGestureClass", "Swipe", "onGestureActivated", "event", "sendEvent", "eventType", "INPUT_MOVE", "isFinal", "<PERSON><PERSON><PERSON><PERSON>", "isGestureRunning", "hasGestureFailed", "INPUT_END", "onRawEvent", "ev", "setTimeout", "cancelEvent", "gesture", "hammer", "get", "options", "enable", "onStart", "getHammerConfig", "pointers", "config", "numberOfPointers", "direction", "getDirection", "getTargetDirections", "directions", "RIGHT", "push", "DIRECTION_RIGHT", "LEFT", "DIRECTION_LEFT", "UP", "DIRECTION_UP", "DOWN", "DIRECTION_DOWN", "getConfig", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "Set", "length", "DIRECTION_NONE", "DIRECTION_ALL", "isGestureEnabledForEvent", "_recognizer", "maxPointers", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "failed", "success", "updateGestureConfig", "props"], "mappings": "AAAA;;AACA;AACA,OAAOA,MAAP,MAAmB,gBAAnB;AAEA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,gBAAT,QAAiC,UAAjC;AACA,OAAOC,sBAAP,MAAmC,0BAAnC;AACA,SAASC,KAAT,QAAsB,SAAtB;;AAGA,MAAMC,mBAAN,SAAkCF,sBAAlC,CAAyD;AAC/C,MAAJG,IAAI,GAAG;AACT,WAAO,OAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOP,MAAM,CAACQ,KAAd;AACD;;AAEDC,EAAAA,kBAAkB,CAACC,KAAD,EAAwB;AACxC,SAAKC,SAAL,CAAe,EACb,GAAGD,KADU;AAEbE,MAAAA,SAAS,EAAEZ,MAAM,CAACa,UAFL;AAGbC,MAAAA,OAAO,EAAE,KAHI;AAIbC,MAAAA,OAAO,EAAE;AAJI,KAAf;AAMA,SAAKC,gBAAL,GAAwB,KAAxB;AACA,SAAKC,gBAAL,GAAwB,KAAxB;AACA,SAAKN,SAAL,CAAe,EACb,GAAGD,KADU;AAEbE,MAAAA,SAAS,EAAEZ,MAAM,CAACkB,SAFL;AAGbJ,MAAAA,OAAO,EAAE;AAHI,KAAf;AAKD;;AAEDK,EAAAA,UAAU,CAACC,EAAD,EAAqB;AAC7B,UAAMD,UAAN,CAAiBC,EAAjB;;AACA,QAAI,KAAKH,gBAAT,EAA2B;AACzB;AACD,KAJ4B,CAK7B;AACA;;;AACA,QAAIG,EAAE,CAACN,OAAP,EAAgB;AACdO,MAAAA,UAAU,CAAC,MAAM;AACf,YAAI,KAAKL,gBAAT,EAA2B;AACzB,eAAKM,WAAL,CAAiBF,EAAjB;AACD;AACF,OAJS,CAAV;AAKD,KAND,MAMO,IAAI,CAAC,KAAKH,gBAAN,IAA0B,CAAC,KAAKD,gBAApC,EAAsD;AAC3D;AACA,YAAMO,OAAO,GAAG,KAAKC,MAAL,CAAaC,GAAb,CAAiB,KAAKnB,IAAtB,CAAhB,CAF2D,CAG3D;;AACA,UAAIiB,OAAO,CAACG,OAAR,CAAgBC,MAAhB,CAAuBJ,OAAvB,EAAgCH,EAAhC,CAAJ,EAAyC;AACvC,aAAKQ,OAAL,CAAaR,EAAb;AACA,aAAKT,SAAL,CAAeS,EAAf;AACD;AACF;AACF;;AAEDS,EAAAA,eAAe,GAAG;AAChB,WAAO;AACL;AACAC,MAAAA,QAAQ,EAAE,KAAKC,MAAL,CAAYC,gBAFjB;AAGLC,MAAAA,SAAS,EAAE,KAAKC,YAAL;AAHN,KAAP;AAKD;;AAEDC,EAAAA,mBAAmB,CAACF,SAAD,EAAoB;AACrC,UAAMG,UAAU,GAAG,EAAnB;;AACA,QAAIH,SAAS,GAAGhC,SAAS,CAACoC,KAA1B,EAAiC;AAC/BD,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAACuC,eAAvB;AACD;;AACD,QAAIN,SAAS,GAAGhC,SAAS,CAACuC,IAA1B,EAAgC;AAC9BJ,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAACyC,cAAvB;AACD;;AACD,QAAIR,SAAS,GAAGhC,SAAS,CAACyC,EAA1B,EAA8B;AAC5BN,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAAC2C,YAAvB;AACD;;AACD,QAAIV,SAAS,GAAGhC,SAAS,CAAC2C,IAA1B,EAAgC;AAC9BR,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAAC6C,cAAvB;AACD,KAboC,CAcrC;;;AACA,WAAOT,UAAP;AACD;;AAEDF,EAAAA,YAAY,GAAG;AACb;AACA,UAAM;AAAED,MAAAA;AAAF,QAAgB,KAAKa,SAAL,EAAtB;AAEA,QAAIV,UAAU,GAAG,EAAjB;;AACA,QAAIH,SAAS,GAAGhC,SAAS,CAACoC,KAA1B,EAAiC;AAC/BD,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAAC+C,oBAAvB;AACD;;AACD,QAAId,SAAS,GAAGhC,SAAS,CAACuC,IAA1B,EAAgC;AAC9BJ,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAAC+C,oBAAvB;AACD;;AACD,QAAId,SAAS,GAAGhC,SAAS,CAACyC,EAA1B,EAA8B;AAC5BN,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAACgD,kBAAvB;AACD;;AACD,QAAIf,SAAS,GAAGhC,SAAS,CAAC2C,IAA1B,EAAgC;AAC9BR,MAAAA,UAAU,CAACE,IAAX,CAAgBtC,MAAM,CAACgD,kBAAvB;AACD;;AACDZ,IAAAA,UAAU,GAAG,CAAC,GAAG,IAAIa,GAAJ,CAAQb,UAAR,CAAJ,CAAb;AAEA,QAAIA,UAAU,CAACc,MAAX,KAAsB,CAA1B,EAA6B,OAAOlD,MAAM,CAACmD,cAAd;AAC7B,QAAIf,UAAU,CAACc,MAAX,KAAsB,CAA1B,EAA6B,OAAOd,UAAU,CAAC,CAAD,CAAjB;AAC7B,WAAOpC,MAAM,CAACoD,aAAd;AACD;;AAEDC,EAAAA,wBAAwB,CACtB;AAAErB,IAAAA;AAAF,GADsB,EAEtBsB,WAFsB,EAGtB;AAAEC,IAAAA,WAAW,EAAEC;AAAf,GAHsB,EAItB;AACA,UAAMC,iBAAiB,GAAGD,aAAa,KAAKxB,gBAA5C;;AACA,QAAI,CAACyB,iBAAD,IAAsB,KAAKzC,gBAA/B,EAAiD;AAC/C,aAAO;AAAE0C,QAAAA,MAAM,EAAE;AAAV,OAAP;AACD;;AACD,WAAO;AAAEC,MAAAA,OAAO,EAAEF;AAAX,KAAP;AACD;;AAEDG,EAAAA,mBAAmB,CAAC;AAAE5B,IAAAA,gBAAgB,GAAG,CAArB;AAAwBC,IAAAA,SAAxB;AAAmC,OAAG4B;AAAtC,GAAD,EAAqD;AACtE,QAAIzD,KAAK,CAAC6B,SAAD,CAAL,IAAoB,OAAOA,SAAP,KAAqB,QAA7C,EAAuD;AACrD,YAAM,IAAI/B,gBAAJ,CAAqB,WAArB,EAAkC+B,SAAlC,EAA6C,QAA7C,CAAN;AACD;;AACD,WAAO,MAAM2B,mBAAN,CAA0B;AAC/B5B,MAAAA,gBAD+B;AAE/BC,MAAAA,SAF+B;AAG/B,SAAG4B;AAH4B,KAA1B,CAAP;AAKD;;AAxHsD;;AA2HzD,eAAexD,mBAAf", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport Hammer from '@egjs/hammerjs';\n\nimport { Direction } from './constants';\nimport { GesturePropError } from './Errors';\nimport DraggingGestureHandler from './DraggingGestureHandler';\nimport { isnan } from './utils';\nimport { HammerInputExt } from './GestureHandler';\n\nclass FlingGestureHandler extends DraggingGestureHandler {\n  get name() {\n    return 'swipe';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Swipe;\n  }\n\n  onGestureActivated(event: HammerInputExt) {\n    this.sendEvent({\n      ...event,\n      eventType: Hammer.INPUT_MOVE,\n      isFinal: false,\n      isFirst: true,\n    });\n    this.isGestureRunning = false;\n    this.hasGestureFailed = false;\n    this.sendEvent({\n      ...event,\n      eventType: Hammer.INPUT_END,\n      isFinal: true,\n    });\n  }\n\n  onRawEvent(ev: HammerInputExt) {\n    super.onRawEvent(ev);\n    if (this.hasGestureFailed) {\n      return;\n    }\n    // <PERSON> doesn't send a `cancel` event for taps.\n    // Manually fail the event.\n    if (ev.isFinal) {\n      setTimeout(() => {\n        if (this.isGestureRunning) {\n          this.cancelEvent(ev);\n        }\n      });\n    } else if (!this.hasGestureFailed && !this.isGestureRunning) {\n      // Tap Gesture start event\n      const gesture = this.hammer!.get(this.name);\n      // @ts-ignore FIXME(TS)\n      if (gesture.options.enable(gesture, ev)) {\n        this.onStart(ev);\n        this.sendEvent(ev);\n      }\n    }\n  }\n\n  getHammerConfig() {\n    return {\n      // @ts-ignore FIXME(TS)\n      pointers: this.config.numberOfPointers,\n      direction: this.getDirection(),\n    };\n  }\n\n  getTargetDirections(direction: number) {\n    const directions = [];\n    if (direction & Direction.RIGHT) {\n      directions.push(Hammer.DIRECTION_RIGHT);\n    }\n    if (direction & Direction.LEFT) {\n      directions.push(Hammer.DIRECTION_LEFT);\n    }\n    if (direction & Direction.UP) {\n      directions.push(Hammer.DIRECTION_UP);\n    }\n    if (direction & Direction.DOWN) {\n      directions.push(Hammer.DIRECTION_DOWN);\n    }\n    // const hammerDirection = directions.reduce((a, b) => a | b, 0);\n    return directions;\n  }\n\n  getDirection() {\n    // @ts-ignore FIXME(TS)\n    const { direction } = this.getConfig();\n\n    let directions = [];\n    if (direction & Direction.RIGHT) {\n      directions.push(Hammer.DIRECTION_HORIZONTAL);\n    }\n    if (direction & Direction.LEFT) {\n      directions.push(Hammer.DIRECTION_HORIZONTAL);\n    }\n    if (direction & Direction.UP) {\n      directions.push(Hammer.DIRECTION_VERTICAL);\n    }\n    if (direction & Direction.DOWN) {\n      directions.push(Hammer.DIRECTION_VERTICAL);\n    }\n    directions = [...new Set(directions)];\n\n    if (directions.length === 0) return Hammer.DIRECTION_NONE;\n    if (directions.length === 1) return directions[0];\n    return Hammer.DIRECTION_ALL;\n  }\n\n  isGestureEnabledForEvent(\n    { numberOfPointers }: any,\n    _recognizer: any,\n    { maxPointers: pointerLength }: any\n  ) {\n    const validPointerCount = pointerLength === numberOfPointers;\n    if (!validPointerCount && this.isGestureRunning) {\n      return { failed: true };\n    }\n    return { success: validPointerCount };\n  }\n\n  updateGestureConfig({ numberOfPointers = 1, direction, ...props }: any) {\n    if (isnan(direction) || typeof direction !== 'number') {\n      throw new GesturePropError('direction', direction, 'number');\n    }\n    return super.updateGestureConfig({\n      numberOfPointers,\n      direction,\n      ...props,\n    });\n  }\n}\n\nexport default FlingGestureHandler;\n"]}