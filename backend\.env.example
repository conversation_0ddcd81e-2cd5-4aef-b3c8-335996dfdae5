# Configuration de la base de données
MONGODB_URI=mongodb://localhost:27017/grosmarket
# Pour MongoDB Atlas: mongodb+srv://username:<EMAIL>/grosmarket

# Configuration JWT
JWT_SECRET=votre_jwt_secret_super_securise_ici
JWT_REFRESH_SECRET=votre_refresh_secret_super_securise_ici

# Configuration du serveur
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Configuration Firebase (pour les notifications push)
FIREBASE_PROJECT_ID=votre-project-id
FIREBASE_PRIVATE_KEY_ID=votre-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nvotre-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=votre-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Configuration Email (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=votre-mot-de-passe-app
EMAIL_FROM=<EMAIL>

# Seuil d'alerte stock
STOCK_ALERT_THRESHOLD=10
