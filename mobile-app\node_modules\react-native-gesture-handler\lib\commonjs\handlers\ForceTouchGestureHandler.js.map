{"version": 3, "sources": ["ForceTouchGestureHandler.ts"], "names": ["forceTouchGestureHandlerProps", "ForceTouchFallback", "React", "Component", "componentDidMount", "console", "warn", "render", "props", "children", "forceTouchHandlerName", "ForceTouchGestureHandler", "PlatformConstants", "forceTouchAvailable", "name", "allowedProps", "baseGestureHandlerProps", "config"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;;;AAMO,MAAMA,6BAA6B,GAAG,CAC3C,UAD2C,EAE3C,UAF2C,EAG3C,sBAH2C,CAAtC,C,CAMP;;;;AACA,MAAMC,kBAAN,SAAiCC,eAAMC,SAAvC,CAA6E;AAE3EC,EAAAA,iBAAiB,GAAG;AAClBC,IAAAA,OAAO,CAACC,IAAR,CACE,uBACE,8NADF,CADF;AAKD;;AACDC,EAAAA,MAAM,GAAG;AACP,WAAO,KAAKC,KAAL,CAAWC,QAAlB;AACD;;AAX0E;;gBAAvER,kB,yBACyB,K;;AAgDxB,MAAMS,qBAAqB,GAAG,0BAA9B;AAEP;AACA;AACA;AACA;;;AACO,MAAMC,wBAAwB,GAAGC,2GAAmBC,mBAAnB,GACpC,4BAGE;AACAC,EAAAA,IAAI,EAAEJ,qBADN;AAEAK,EAAAA,YAAY,EAAE,CACZ,GAAGC,6CADS,EAEZ,GAAGhB,6BAFS,CAFd;AAMAiB,EAAAA,MAAM,EAAE;AANR,CAHF,CADoC,GAYpChB,kBAZG;;AAcNU,wBAAD,CAAuDE,mBAAvD,GACE,oHAAmBA,mBAAnB,KAA0C,KAD5C", "sourcesContent": ["import React, { PropsWithChildren } from 'react';\nimport { tagMessage } from '../utils';\nimport PlatformConstants from '../PlatformConstants';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\nimport type { ForceTouchGestureHandlerEventPayload } from './GestureHandlerEventPayload';\n\nexport const forceTouchGestureHandlerProps = [\n  'minForce',\n  'maxForce',\n  'feedbackOnActivation',\n] as const;\n\n// implicit `children` prop has been removed in @types/react^18.0.0\nclass ForceTouchFallback extends React.Component<PropsWithChildren<unknown>> {\n  static forceTouchAvailable = false;\n  componentDidMount() {\n    console.warn(\n      tagMessage(\n        'ForceTouchGestureHandler is not available on this platform. Please use ForceTouchGestureHandler.forceTouchAvailable to conditionally render other components that would provide a fallback behavior specific to your usecase'\n      )\n    );\n  }\n  render() {\n    return this.props.children;\n  }\n}\n\nexport interface ForceTouchGestureConfig {\n  /**\n   *\n   * A minimal pressure that is required before handler can activate. Should be a\n   * value from range `[0.0, 1.0]`. Default is `0.2`.\n   */\n  minForce?: number;\n\n  /**\n   * A maximal pressure that could be applied for handler. If the pressure is\n   * greater, handler fails. Should be a value from range `[0.0, 1.0]`.\n   */\n  maxForce?: number;\n\n  /**\n   * Boolean value defining if haptic feedback has to be performed on\n   * activation.\n   */\n  feedbackOnActivation?: boolean;\n}\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\nexport interface ForceTouchGestureHandlerProps\n  extends BaseGestureHandlerProps<ForceTouchGestureHandlerEventPayload>,\n    ForceTouchGestureConfig {}\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\nexport type ForceTouchGestureHandler = typeof ForceTouchGestureHandler & {\n  forceTouchAvailable: boolean;\n};\n\nexport const forceTouchHandlerName = 'ForceTouchGestureHandler';\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const ForceTouchGestureHandler = PlatformConstants?.forceTouchAvailable\n  ? createHandler<\n      ForceTouchGestureHandlerProps,\n      ForceTouchGestureHandlerEventPayload\n    >({\n      name: forceTouchHandlerName,\n      allowedProps: [\n        ...baseGestureHandlerProps,\n        ...forceTouchGestureHandlerProps,\n      ] as const,\n      config: {},\n    })\n  : ForceTouchFallback;\n\n(ForceTouchGestureHandler as ForceTouchGestureHandler).forceTouchAvailable =\n  PlatformConstants?.forceTouchAvailable || false;\n"]}