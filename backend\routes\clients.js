const express = require('express');
const router = express.Router();
const Client = require('../models/Client');
const { validate, schemas } = require('../middleware/validation');
const { authenticateClient } = require('../middleware/auth');

// Toutes les routes nécessitent une authentification client
router.use(authenticateClient);

// Mettre à jour le profil client
router.put('/profile', 
  validate(schemas.clientProfile), 
  async (req, res) => {
    try {
      const clientId = req.user._id;
      const updates = req.body;

      // Vérifier si l'email ou le téléphone sont déjà utilisés
      if (updates.email) {
        const existingClient = await Client.findOne({ 
          email: updates.email, 
          _id: { $ne: clientId } 
        });
        if (existingClient) {
          return res.status(400).json({
            message: 'Cet email est déjà utilisé par un autre compte'
          });
        }
      }

      if (updates.telephone) {
        const existingPhone = await Client.findOne({ 
          telephone: updates.telephone, 
          _id: { $ne: clientId } 
        });
        if (existingPhone) {
          return res.status(400).json({
            message: 'Ce numéro de téléphone est déjà utilisé par un autre compte'
          });
        }
      }

      const client = await Client.findByIdAndUpdate(
        clientId,
        { $set: updates },
        { new: true, runValidators: true }
      ).select('-motDePasse');

      if (!client) {
        return res.status(404).json({
          message: 'Client non trouvé'
        });
      }

      res.json({
        message: 'Profil mis à jour avec succès',
        client: client.toPublicJSON()
      });

    } catch (error) {
      console.error('Erreur mise à jour profil:', error);
      res.status(500).json({
        message: 'Erreur lors de la mise à jour du profil',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
);

// Obtenir le profil complet du client
router.get('/profile', async (req, res) => {
  try {
    const client = await Client.findById(req.user._id).select('-motDePasse');
    
    if (!client) {
      return res.status(404).json({
        message: 'Client non trouvé'
      });
    }

    res.json(client.toPublicJSON());

  } catch (error) {
    console.error('Erreur récupération profil:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du profil',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

// Supprimer le compte client (désactivation)
router.delete('/account', async (req, res) => {
  try {
    const clientId = req.user._id;
    const { motDePasse } = req.body;

    if (!motDePasse) {
      return res.status(400).json({
        message: 'Mot de passe requis pour supprimer le compte'
      });
    }

    const client = await Client.findById(clientId);
    if (!client) {
      return res.status(404).json({
        message: 'Client non trouvé'
      });
    }

    // Vérifier le mot de passe
    const isPasswordValid = await client.comparePassword(motDePasse);
    if (!isPasswordValid) {
      return res.status(400).json({
        message: 'Mot de passe incorrect'
      });
    }

    // Désactiver le compte au lieu de le supprimer
    client.isActive = false;
    client.fcmToken = null;
    await client.save();

    res.json({
      message: 'Compte désactivé avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression compte:', error);
    res.status(500).json({
      message: 'Erreur lors de la suppression du compte',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
