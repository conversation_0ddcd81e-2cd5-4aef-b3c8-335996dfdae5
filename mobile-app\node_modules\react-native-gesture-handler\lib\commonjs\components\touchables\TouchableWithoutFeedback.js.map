{"version": 3, "sources": ["TouchableWithoutFeedback.tsx"], "names": ["TouchableWithoutFeedback", "React", "forwardRef", "delayLongPress", "extraButtonProps", "rippleColor", "exclusive", "rest", "ref"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;;AAQA;AACA;AACA;AACA,MAAMA,wBAAwB,gBAAGC,KAAK,CAACC,UAAN,CAI/B,CACE;AACEC,EAAAA,cAAc,GAAG,GADnB;AAEEC,EAAAA,gBAAgB,GAAG;AACjBC,IAAAA,WAAW,EAAE,aADI;AAEjBC,IAAAA,SAAS,EAAE;AAFM,GAFrB;AAME,KAAGC;AANL,CADF,EAUEC,GAVF,kBAYE,oBAAC,yBAAD;AACE,EAAA,GAAG,EAAEA,GADP;AAEE,EAAA,cAAc,EAAEL,cAFlB;AAGE,EAAA,gBAAgB,EAAEC;AAHpB,GAIMG,IAJN,EAhB6B,CAAjC;eAyBeP,wB", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport GenericTouchable from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\n\n/**\n * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nexport type TouchableWithoutFeedbackProps = GenericTouchableProps;\n\n/**\n * @deprecated TouchableWithoutFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nconst TouchableWithoutFeedback = React.forwardRef<\n  GenericTouchable,\n  PropsWithChildren<TouchableWithoutFeedbackProps>\n>(\n  (\n    {\n      delayLongPress = 600,\n      extraButtonProps = {\n        rippleColor: 'transparent',\n        exclusive: true,\n      },\n      ...rest\n    },\n\n    ref\n  ) => (\n    <GenericTouchable\n      ref={ref}\n      delayLongPress={delayLongPress}\n      extraButtonProps={extraButtonProps}\n      {...rest}\n    />\n  )\n);\n\nexport default TouchableWithoutFeedback;\n"]}