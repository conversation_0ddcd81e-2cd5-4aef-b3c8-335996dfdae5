{"version": 3, "sources": ["ReanimatedDrawerLayout.tsx"], "names": ["DRAG_TOSS", "DrawerPosition", "DrawerState", "DrawerType", "DrawerLockMode", "Drawer<PERSON>eyboardDismissMode", "defaultProps", "drawerWidth", "drawerPosition", "LEFT", "drawerType", "FRONT", "edgeWidth", "minSwipeDistance", "overlayColor", "drawerLockMode", "UNLOCKED", "enableTrackpadTwoFingerGesture", "activeCursor", "mouseButton", "MouseB<PERSON>on", "statusBarAnimation", "setStatusBarHidden", "StatusBar", "setHidden", "dismissKeyboard", "Keyboard", "dismiss", "DrawerLayout", "props", "ref", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drawerState", "setDrawerState", "IDLE", "drawerOpened", "set<PERSON><PERSON>er<PERSON><PERSON>ed", "drawerBackgroundColor", "drawerContainerStyle", "contentContainerStyle", "hideStatusBar", "keyboardDismissMode", "userSelect", "enableContextMenu", "renderNavigationView", "onDrawerSlide", "onDrawerClose", "onDrawerOpen", "onDrawerStateChanged", "animationSpeed", "animationSpeedProp", "isFromLeft", "sideCorrection", "openValue", "value", "isDrawerOpen", "handleContainerLayout", "nativeEvent", "layout", "width", "emitStateChanged", "newState", "drawerWillShow", "drawerAnimatedProps", "accessibilityViewIsModal", "overlayAnimatedProps", "pointerEvents", "edgeHitSlop", "setEdgeHitSlop", "left", "right", "gestureOrientation", "animateDrawer", "toValue", "initialVelocity", "willShow", "SETTLING", "normalizedToValue", "Extrapolation", "CLAMP", "normalizedInitialVelocity", "overshootClamping", "velocity", "mass", "damping", "stiffness", "finished", "handleRelease", "event", "translationX", "dragX", "velocityX", "x", "touchX", "gestureStartX", "dragOffsetBasedOnStart", "startOffsetX", "projOffsetX", "shouldOpen", "openDrawer", "options", "closeDrawer", "overlayDismissGesture", "Gesture", "Tap", "maxDistance", "onEnd", "LOCKED_OPEN", "overlayAnimatedStyle", "opacity", "backgroundColor", "fillHitSlop", "panGesture", "Pan", "hitSlop", "minDistance", "activeOffsetX", "failOffsetY", "simultaneousWithExternalGesture", "enabled", "LOCKED_CLOSED", "onStart", "DRAGGING", "ON_DRAG", "onUpdate", "startedOutsideTranslation", "startedInsideTranslation", "adjustedTranslation", "Math", "max", "reverseContentDirection", "I18nManager", "isRTL", "dynamicDrawerStyles", "containerStyles", "transform", "translateX", "drawerAnimatedStyle", "closedDrawerOffset", "isBack", "BACK", "isIdle", "flexDirection", "containerAnimatedProps", "importantForAccessibility", "Platform", "OS", "undefined", "children", "styles", "main", "containerOnBack", "containerInFront", "overlay", "drawerContainer", "StyleSheet", "create", "absoluteFillObject", "zIndex", "flex", "overflow"], "mappings": ";;;;;;;AAIA;;AAUA;;AAYA;;AAYA;;AACA;;AACA;;;;;;AAxCA;AACA;AACA;AA+CA,MAAMA,SAAS,GAAG,IAAlB;IAEYC,c;;;WAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;GAAAA,c,8BAAAA,c;;IAKAC,W;;;WAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;GAAAA,W,2BAAAA,W;;IAMAC,U;;;WAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;GAAAA,U,0BAAAA,U;;IAMAC,c;;;WAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;GAAAA,c,8BAAAA,c;;IAMAC,yB;;;WAAAA,yB;AAAAA,EAAAA,yB,CAAAA,yB;AAAAA,EAAAA,yB,CAAAA,yB;GAAAA,yB,yCAAAA,yB;;AAgLZ,MAAMC,YAAY,GAAG;AACnBC,EAAAA,WAAW,EAAE,GADM;AAEnBC,EAAAA,cAAc,EAAEP,cAAc,CAACQ,IAFZ;AAGnBC,EAAAA,UAAU,EAAEP,UAAU,CAACQ,KAHJ;AAInBC,EAAAA,SAAS,EAAE,EAJQ;AAKnBC,EAAAA,gBAAgB,EAAE,CALC;AAMnBC,EAAAA,YAAY,EAAE,oBANK;AAOnBC,EAAAA,cAAc,EAAEX,cAAc,CAACY,QAPZ;AAQnBC,EAAAA,8BAA8B,EAAE,KARb;AASnBC,EAAAA,YAAY,EAAE,MATK;AAUnBC,EAAAA,WAAW,EAAEC,kCAAYX,IAVN;AAWnBY,EAAAA,kBAAkB,EAAE;AAXD,CAArB,C,CAcA;;AACA,MAAMC,kBAAkB,GAAGC,uBAAUC,SAArC;AACA,MAAMC,eAAe,GAAGC,sBAASC,OAAjC;AAEA,MAAMC,YAAY,gBAAG,uBACnB,SAASA,YAAT,CAAsBC,KAAtB,EAAgDC,GAAhD,EAAqD;AACnD,QAAM,CAACC,cAAD,EAAiBC,iBAAjB,IAAsC,qBAAS,CAAT,CAA5C;AACA,QAAM,CAACC,WAAD,EAAcC,cAAd,IAAgC,qBACpChC,WAAW,CAACiC,IADwB,CAAtC;AAGA,QAAM,CAACC,YAAD,EAAeC,eAAf,IAAkC,qBAAS,KAAT,CAAxC;AAEA,QAAM;AACJ7B,IAAAA,cAAc,GAAGF,YAAY,CAACE,cAD1B;AAEJD,IAAAA,WAAW,GAAGD,YAAY,CAACC,WAFvB;AAGJG,IAAAA,UAAU,GAAGJ,YAAY,CAACI,UAHtB;AAIJ4B,IAAAA,qBAJI;AAKJC,IAAAA,oBALI;AAMJC,IAAAA,qBANI;AAOJ3B,IAAAA,gBAAgB,GAAGP,YAAY,CAACO,gBAP5B;AAQJD,IAAAA,SAAS,GAAGN,YAAY,CAACM,SARrB;AASJG,IAAAA,cAAc,GAAGT,YAAY,CAACS,cAT1B;AAUJD,IAAAA,YAAY,GAAGR,YAAY,CAACQ,YAVxB;AAWJG,IAAAA,8BAA8B,GAAGX,YAAY,CAACW,8BAX1C;AAYJC,IAAAA,YAAY,GAAGZ,YAAY,CAACY,YAZxB;AAaJC,IAAAA,WAAW,GAAGb,YAAY,CAACa,WAbvB;AAcJE,IAAAA,kBAAkB,GAAGf,YAAY,CAACe,kBAd9B;AAeJoB,IAAAA,aAfI;AAgBJC,IAAAA,mBAhBI;AAiBJC,IAAAA,UAjBI;AAkBJC,IAAAA,iBAlBI;AAmBJC,IAAAA,oBAnBI;AAoBJC,IAAAA,aApBI;AAqBJC,IAAAA,aArBI;AAsBJC,IAAAA,YAtBI;AAuBJC,IAAAA,oBAvBI;AAwBJC,IAAAA,cAAc,EAAEC;AAxBZ,MAyBFtB,KAzBJ;AA2BA,QAAMuB,UAAU,GAAG5C,cAAc,KAAKP,cAAc,CAACQ,IAArD;AAEA,QAAM4C,cAAc,GAAGD,UAAU,GAAG,CAAH,GAAO,CAAC,CAAzC,CApCmD,CAsCnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAME,SAAS,GAAG,2CAAuB,CAAvB,CAAlB;AAEA,8CAAgB,MAAM;AACpBR,IAAAA,aAAa,IAAI,oCAAQA,aAAR,EAAuBQ,SAAS,CAACC,KAAjC,CAAjB;AACD,GAFD,EAEG,EAFH;AAIA,QAAMC,YAAY,GAAG,2CAAe,KAAf,CAArB;;AAEA,QAAMC,qBAAqB,GAAG,CAAC;AAAEC,IAAAA;AAAF,GAAD,KAAwC;AACpE1B,IAAAA,iBAAiB,CAAC0B,WAAW,CAACC,MAAZ,CAAmBC,KAApB,CAAjB;AACD,GAFD;;AAIA,QAAMC,gBAAgB,GAAG,wBACvB,CAACC,QAAD,EAAwBC,cAAxB,KAAoD;AAClD;;AADkD;;AAElDd,IAAAA,oBAAoB,iBAClB,oCAAQA,oBAAR,CADkB,6CAClB,SAAgCa,QAAhC,EAA0CC,cAA1C,CADkB,CAApB;AAED,GALsB,EAMvB,CAACd,oBAAD,CANuB,CAAzB;AASA,QAAMe,mBAAmB,GAAG,6CAAiB,OAAO;AAClDC,IAAAA,wBAAwB,EAAET,YAAY,CAACD;AADW,GAAP,CAAjB,CAA5B;AAIA,QAAMW,oBAAoB,GAAG,6CAAiB,OAAO;AACnDC,IAAAA,aAAa,EAAEX,YAAY,CAACD,KAAb,GAAsB,MAAtB,GAA0C;AADN,GAAP,CAAjB,CAA7B,CA1EmD,CA8EnD;AACA;;AACA,QAAM,CAACa,WAAD,EAAcC,cAAd,IAAgC,qBACpCjB,UAAU,GACN;AAAEkB,IAAAA,IAAI,EAAE,CAAR;AAAWV,IAAAA,KAAK,EAAEhD;AAAlB,GADM,GAEN;AAAE2D,IAAAA,KAAK,EAAE,CAAT;AAAYX,IAAAA,KAAK,EAAEhD;AAAnB,GAHgC,CAAtC,CAhFmD,CAsFnD;;AACA,QAAM4D,kBAAkB,GAAG,oBACzB,MAAMnB,cAAc,IAAIjB,YAAY,GAAG,CAAC,CAAJ,GAAQ,CAAxB,CADK,EAEzB,CAACiB,cAAD,EAAiBjB,YAAjB,CAFyB,CAA3B;AAKA,wBAAU,MAAM;AACdiC,IAAAA,cAAc,CACZjB,UAAU,GACN;AAAEkB,MAAAA,IAAI,EAAE,CAAR;AAAWV,MAAAA,KAAK,EAAEhD;AAAlB,KADM,GAEN;AAAE2D,MAAAA,KAAK,EAAE,CAAT;AAAYX,MAAAA,KAAK,EAAEhD;AAAnB,KAHQ,CAAd;AAKD,GAND,EAMG,CAACwC,UAAD,EAAaxC,SAAb,CANH;AAQA,QAAM6D,aAAa,GAAG,wBACpB,CAACC,OAAD,EAAkBC,eAAlB,EAA2CzB,cAA3C,KAAuE;AACrE;;AACA,UAAM0B,QAAQ,GAAGF,OAAO,KAAK,CAA7B;AACAlB,IAAAA,YAAY,CAACD,KAAb,GAAqBqB,QAArB;AAEAf,IAAAA,gBAAgB,CAAC3D,WAAW,CAAC2E,QAAb,EAAuBD,QAAvB,CAAhB;AACA,wCAAQ1C,cAAR,EAAwBhC,WAAW,CAAC2E,QAApC;;AAEA,QAAIpC,aAAJ,EAAmB;AACjB,0CAAQnB,kBAAR,EAA4BsD,QAA5B,EAAsCvD,kBAAtC;AACD;;AAED,UAAMyD,iBAAiB,GAAG,wCACxBJ,OADwB,EAExB,CAAC,CAAD,EAAInE,WAAJ,CAFwB,EAGxB,CAAC,CAAD,EAAI,CAAJ,CAHwB,EAIxBwE,qCAAcC,KAJU,CAA1B;AAOA,UAAMC,yBAAyB,GAAG,wCAChCN,eADgC,EAEhC,CAAC,CAAD,EAAIpE,WAAJ,CAFgC,EAGhC,CAAC,CAAD,EAAI,CAAJ,CAHgC,EAIhCwE,qCAAcC,KAJkB,CAAlC;AAOA1B,IAAAA,SAAS,CAACC,KAAV,GAAkB,uCAChBuB,iBADgB,EAEhB;AACEI,MAAAA,iBAAiB,EAAE,IADrB;AAEEC,MAAAA,QAAQ,EAAEF,yBAFZ;AAGEG,MAAAA,IAAI,EAAElC,cAAc,GAChB,IAAIA,cADY,GAEhB,KAAKC,kBAAL,aAAKA,kBAAL,cAAKA,kBAAL,GAA2B,CAA3B,CALN;AAMEkC,MAAAA,OAAO,EAAE,EANX;AAOEC,MAAAA,SAAS,EAAE;AAPb,KAFgB,EAWfC,QAAD,IAAc;AACZ,UAAIA,QAAJ,EAAc;AACZ1B,QAAAA,gBAAgB,CAAC3D,WAAW,CAACiC,IAAb,EAAmByC,QAAnB,CAAhB;AACA,4CAAQvC,eAAR,EAAyBuC,QAAzB;AACA,4CAAQ1C,cAAR,EAAwBhC,WAAW,CAACiC,IAApC;;AACA,YAAIyC,QAAJ,EAAc;AAAA;;AACZ5B,UAAAA,YAAY,kBAAI,oCAAQA,YAAR,CAAJ,8CAAI,WAAJ,CAAZ;AACD,SAFD,MAEO;AAAA;;AACLD,UAAAA,aAAa,kBAAI,oCAAQA,aAAR,CAAJ,8CAAI,WAAJ,CAAb;AACD;AACF;AACF,KAtBe,CAAlB;AAwBD,GAnDmB,EAoDpB,CACEO,SADF,EAEEO,gBAFF,EAGEL,YAHF,EAIEf,aAJF,EAKEM,aALF,EAMEC,YANF,EAOEzC,WAPF,EAQEc,kBARF,CApDoB,CAAtB;AAgEA,QAAMmE,aAAa,GAAG,wBACnBC,KAAD,IAAmE;AACjE;;AACA,QAAI;AAAEC,MAAAA,YAAY,EAAEC,KAAhB;AAAuBC,MAAAA,SAAvB;AAAkCC,MAAAA,CAAC,EAAEC;AAArC,QAAgDL,KAApD;;AAEA,QAAIjF,cAAc,KAAKP,cAAc,CAACQ,IAAtC,EAA4C;AAC1C;AACA;AACAkF,MAAAA,KAAK,GAAG,CAACA,KAAT;AACAG,MAAAA,MAAM,GAAG/D,cAAc,GAAG+D,MAA1B;AACAF,MAAAA,SAAS,GAAG,CAACA,SAAb;AACD;;AAED,UAAMG,aAAa,GAAGD,MAAM,GAAGH,KAA/B;AACA,QAAIK,sBAAsB,GAAG,CAA7B;;AAEA,QAAItF,UAAU,KAAKP,UAAU,CAACQ,KAA9B,EAAqC;AACnCqF,MAAAA,sBAAsB,GACpBD,aAAa,GAAGxF,WAAhB,GAA8BwF,aAAa,GAAGxF,WAA9C,GAA4D,CAD9D;AAED;;AAED,UAAM0F,YAAY,GAChBN,KAAK,GACLK,sBADA,IAECxC,YAAY,CAACD,KAAb,GAAqBhD,WAArB,GAAmC,CAFpC,CADF;AAKA,UAAM2F,WAAW,GAAGD,YAAY,GAAGjG,SAAS,GAAG4F,SAA/C;AAEA,UAAMO,UAAU,GAAGD,WAAW,GAAG3F,WAAW,GAAG,CAA/C;;AAEA,QAAI4F,UAAJ,EAAgB;AACd1B,MAAAA,aAAa,CAAClE,WAAD,EAAcqF,SAAd,CAAb;AACD,KAFD,MAEO;AACLnB,MAAAA,aAAa,CAAC,CAAD,EAAImB,SAAJ,CAAb;AACD;AACF,GAnCmB,EAoCpB,CACEnB,aADF,EAEE1C,cAFF,EAGEvB,cAHF,EAIEE,UAJF,EAKEH,WALF,EAMEiD,YANF,CApCoB,CAAtB;AA8CA,QAAM4C,UAAU,GAAG,wBACjB,CAACC,OAA6B,GAAG,EAAjC,KAAwC;AACtC;;AADsC;;AAEtC5B,IAAAA,aAAa,CACXlE,WADW,2BAEX8F,OAAO,CAAC1B,eAFG,yEAEgB,CAFhB,EAGX0B,OAAO,CAACnD,cAHG,CAAb;AAKD,GARgB,EASjB,CAACuB,aAAD,EAAgBlE,WAAhB,CATiB,CAAnB;AAYA,QAAM+F,WAAW,GAAG,wBAClB,CAACD,OAA6B,GAAG,EAAjC,KAAwC;AACtC;;AADsC;;AAEtC5B,IAAAA,aAAa,CAAC,CAAD,4BAAI4B,OAAO,CAAC1B,eAAZ,2EAA+B,CAA/B,EAAkC0B,OAAO,CAACnD,cAA1C,CAAb;AACD,GAJiB,EAKlB,CAACuB,aAAD,CALkB,CAApB;AAQA,QAAM8B,qBAAqB,GAAG,oBAC5B,MACEC,+BAAQC,GAAR,GACGC,WADH,CACe,EADf,EAEGC,KAFH,CAES,MAAM;AACX,QACEnD,YAAY,CAACD,KAAb,IACAxC,cAAc,KAAKX,cAAc,CAACwG,WAFpC,EAGE;AACAN,MAAAA,WAAW;AACZ;AACF,GATH,CAF0B,EAY5B,CAACA,WAAD,EAAc9C,YAAd,EAA4BzC,cAA5B,CAZ4B,CAA9B;AAeA,QAAM8F,oBAAoB,GAAG,6CAAiB,OAAO;AACnDC,IAAAA,OAAO,EAAExD,SAAS,CAACC,KADgC;AAEnDwD,IAAAA,eAAe,EAAEjG;AAFkC,GAAP,CAAjB,CAA7B;AAKA,QAAMkG,WAAW,GAAG,oBAClB,MAAO5D,UAAU,GAAG;AAAEkB,IAAAA,IAAI,EAAE/D;AAAR,GAAH,GAA2B;AAAEgE,IAAAA,KAAK,EAAEhE;AAAT,GAD1B,EAElB,CAACA,WAAD,EAAc6C,UAAd,CAFkB,CAApB;AAKA,QAAM6D,UAAU,GAAG,oBAAQ,MAAM;AAC/B,WAAOT,+BAAQU,GAAR,GACJhG,YADI,CACSA,YADT,EAEJC,WAFI,CAEQA,WAFR,EAGJgG,OAHI,CAGI/E,YAAY,GAAG4E,WAAH,GAAiB5C,WAHjC,EAIJgD,WAJI,CAIQhF,YAAY,GAAG,GAAH,GAAS,CAJ7B,EAKJiF,aALI,CAKU7C,kBAAkB,GAAG3D,gBAL/B,EAMJyG,WANI,CAMQ,CAAC,CAAC,EAAF,EAAM,EAAN,CANR,EAOJC,+BAPI,CAO4BhB,qBAP5B,EAQJtF,8BARI,CAQ2BA,8BAR3B,EASJuG,OATI,CAUHvF,WAAW,KAAK/B,WAAW,CAAC2E,QAA5B,KACGzC,YAAY,GACTrB,cAAc,KAAKX,cAAc,CAACwG,WADzB,GAET7F,cAAc,KAAKX,cAAc,CAACqH,aAHxC,CAVG,EAeJC,OAfI,CAeI,MAAM;AACb7D,MAAAA,gBAAgB,CAAC3D,WAAW,CAACyH,QAAb,EAAuB,KAAvB,CAAhB;AACA,0CAAQzF,cAAR,EAAwBhC,WAAW,CAACyH,QAApC;;AACA,UAAIjF,mBAAmB,KAAKrC,yBAAyB,CAACuH,OAAtD,EAA+D;AAC7D,4CAAQnG,eAAR;AACD;;AACD,UAAIgB,aAAJ,EAAmB;AACjB,4CAAQnB,kBAAR,EAA4B,IAA5B,EAAkCD,kBAAlC;AACD;AACF,KAxBI,EAyBJwG,QAzBI,CAyBMpC,KAAD,IAAW;AACnB,YAAMqC,yBAAyB,GAAG1E,UAAU,GACxC,wCACEqC,KAAK,CAACI,CADR,EAEE,CAAC,CAAD,EAAItF,WAAJ,EAAiBA,WAAW,GAAG,CAA/B,CAFF,EAGE,CAAC,CAAD,EAAIA,WAAJ,EAAiBA,WAAjB,CAHF,CADwC,GAMxC,wCACEkF,KAAK,CAACI,CAAN,GAAU9D,cADZ,EAEE,CAAC,CAACxB,WAAD,GAAe,CAAhB,EAAmB,CAACA,WAApB,EAAiC,CAAjC,CAFF,EAGE,CAACA,WAAD,EAAcA,WAAd,EAA2B,CAA3B,CAHF,CANJ;AAYA,YAAMwH,wBAAwB,GAC5B1E,cAAc,IACboC,KAAK,CAACC,YAAN,IACEtD,YAAY,GAAG7B,WAAW,GAAG,CAACiE,kBAAlB,GAAuC,CADrD,CADa,CADhB;AAKA,YAAMwD,mBAAmB,GAAGC,IAAI,CAACC,GAAL,CAC1B9F,YAAY,GAAG0F,yBAAH,GAA+B,CADjB,EAE1BC,wBAF0B,CAA5B;AAKAzE,MAAAA,SAAS,CAACC,KAAV,GAAkB,wCAChByE,mBADgB,EAEhB,CAAC,CAACzH,WAAF,EAAe,CAAf,EAAkBA,WAAlB,CAFgB,EAGhB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHgB,EAIhBwE,qCAAcC,KAJE,CAAlB;AAMD,KAtDI,EAuDJ2B,KAvDI,CAuDEnB,aAvDF,CAAP;AAwDD,GAzDkB,EAyDhB,CACDzE,cADC,EAEDuC,SAFC,EAGD/C,WAHC,EAIDsD,gBAJC,EAKDW,kBALC,EAMDgB,aANC,EAODpB,WAPC,EAQD4C,WARC,EASDnG,gBATC,EAUD4B,aAVC,EAWDC,mBAXC,EAYD6D,qBAZC,EAaDnE,YAbC,EAcDgB,UAdC,EAeDrB,cAfC,EAgBDsB,cAhBC,EAiBDpB,WAjBC,EAkBDf,YAlBC,EAmBDD,8BAnBC,EAoBDE,WApBC,EAqBDE,kBArBC,CAzDgB,CAAnB,CA/PmD,CAgVnD;;AACA,QAAM8G,uBAAuB,GAAGC,yBAAYC,KAAZ,GAC5BjF,UAD4B,GAE5B,CAACA,UAFL;AAIA,QAAMkF,mBAAmB,GAAG;AAC1BvB,IAAAA,eAAe,EAAEzE,qBADS;AAE1BsB,IAAAA,KAAK,EAAErD;AAFmB,GAA5B;AAKA,QAAMgI,eAAe,GAAG,6CAAiB,MAAM;AAC7C,QAAI7H,UAAU,KAAKP,UAAU,CAACQ,KAA9B,EAAqC;AACnC,aAAO,EAAP;AACD;;AAED,WAAO;AACL6H,MAAAA,SAAS,EAAE,CACT;AACEC,QAAAA,UAAU,EAAE,wCACVnF,SAAS,CAACC,KADA,EAEV,CAAC,CAAD,EAAI,CAAJ,CAFU,EAGV,CAAC,CAAD,EAAIhD,WAAW,GAAG8C,cAAlB,CAHU,EAIV0B,qCAAcC,KAJJ;AADd,OADS;AADN,KAAP;AAYD,GAjBuB,CAAxB;AAmBA,QAAM0D,mBAAmB,GAAG,6CAAiB,MAAM;AACjD,UAAMC,kBAAkB,GAAGpI,WAAW,GAAG,CAAC8C,cAA1C;AACA,UAAMuF,MAAM,GAAGlI,UAAU,KAAKP,UAAU,CAAC0I,IAAzC;AACA,UAAMC,MAAM,GAAG7G,WAAW,KAAK/B,WAAW,CAACiC,IAA3C;;AAEA,QAAIyG,MAAJ,EAAY;AACV,aAAO;AACLJ,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAE;AAAd,SAAD,CADN;AAELM,QAAAA,aAAa,EAAEZ,uBAAuB,GAAG,aAAH,GAAmB;AAFpD,OAAP;AAID;;AAED,QAAIM,UAAU,GAAG,CAAjB;;AAEA,QAAIK,MAAJ,EAAY;AACVL,MAAAA,UAAU,GAAGrG,YAAY,GAAG,CAAH,GAAOuG,kBAAhC;AACD,KAFD,MAEO;AACLF,MAAAA,UAAU,GAAG,wCACXnF,SAAS,CAACC,KADC,EAEX,CAAC,CAAD,EAAI,CAAJ,CAFW,EAGX,CAACoF,kBAAD,EAAqB,CAArB,CAHW,EAIX5D,qCAAcC,KAJH,CAAb;AAMD;;AAED,WAAO;AACLwD,MAAAA,SAAS,EAAE,CAAC;AAAEC,QAAAA;AAAF,OAAD,CADN;AAELM,MAAAA,aAAa,EAAEZ,uBAAuB,GAAG,aAAH,GAAmB;AAFpD,KAAP;AAID,GA7B2B,CAA5B;AA+BA,QAAMa,sBAAsB,GAAG,6CAAiB,OAAO;AACrDC,IAAAA,yBAAyB,EACvBC,sBAASC,EAAT,KAAgB,SAAhB,GACI3F,YAAY,CAACD,KAAb,GACG,qBADH,GAEG,KAHP,GAII6F;AAN+C,GAAP,CAAjB,CAA/B;AASA,QAAMC,QAAQ,GACZ,OAAOxH,KAAK,CAACwH,QAAb,KAA0B,UAA1B,GACIxH,KAAK,CAACwH,QAAN,CAAe/F,SAAf,CADJ,CAC8B;AAD9B,IAEIzB,KAAK,CAACwH,QAHZ;AAKA,kCACEvH,GADF,EAEE,OAAO;AACLsE,IAAAA,UADK;AAELE,IAAAA;AAFK,GAAP,CAFF,EAME,CAACF,UAAD,EAAaE,WAAb,CANF;AASA,sBACE,6BAAC,gCAAD;AACE,IAAA,OAAO,EAAEW,UADX;AAEE,IAAA,UAAU,EAAEtE,UAFd;AAGE,IAAA,iBAAiB,EAAEC;AAHrB,kBAIE,6BAAC,8BAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE0G,MAAM,CAACC,IAA7B;AAAmC,IAAA,QAAQ,EAAE9F;AAA7C,kBACE,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAE8C;AAA1B,kBACE,6BAAC,8BAAD,CAAU,IAAV;AACE,IAAA,KAAK,EAAE,CACL7F,UAAU,KAAKP,UAAU,CAACQ,KAA1B,GACI2I,MAAM,CAACE,eADX,GAEIF,MAAM,CAACG,gBAHN,EAILlB,eAJK,EAKL/F,qBALK,CADT;AAQE,IAAA,aAAa,EAAEwG;AARjB,KASGK,QATH,eAUE,6BAAC,8BAAD,CAAU,IAAV;AACE,IAAA,aAAa,EAAEnF,oBADjB;AAEE,IAAA,KAAK,EAAE,CAACoF,MAAM,CAACI,OAAR,EAAiB7C,oBAAjB;AAFT,IAVF,CADF,CADF,eAkBE,6BAAC,8BAAD,CAAU,IAAV;AACE,IAAA,aAAa,EAAC,UADhB;AAEE,IAAA,aAAa,EAAE7C,mBAFjB;AAGE,IAAA,KAAK,EAAE,CACLsF,MAAM,CAACK,eADF,EAELjB,mBAFK,EAGLnG,oBAHK;AAHT,kBAQE,6BAAC,8BAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE+F;AAAtB,KACGzF,oBAAoB,CAACS,SAAD,CADvB,CARF,CAlBF,CAJF,CADF;AAsCD,CA1ckB,CAArB;eA6ce1B,Y;;;AAEf,MAAM0H,MAAM,GAAGM,wBAAWC,MAAX,CAAkB;AAC/BF,EAAAA,eAAe,EAAE,EACf,GAAGC,wBAAWE,kBADC;AAEfC,IAAAA,MAAM,EAAE,IAFO;AAGfhB,IAAAA,aAAa,EAAE;AAHA,GADc;AAM/BU,EAAAA,gBAAgB,EAAE,EAChB,GAAGG,wBAAWE,kBADE;AAEhBC,IAAAA,MAAM,EAAE;AAFQ,GANa;AAU/BP,EAAAA,eAAe,EAAE,EACf,GAAGI,wBAAWE;AADC,GAVc;AAa/BP,EAAAA,IAAI,EAAE;AACJS,IAAAA,IAAI,EAAE,CADF;AAEJD,IAAAA,MAAM,EAAE,CAFJ;AAGJE,IAAAA,QAAQ,EAAE;AAHN,GAbyB;AAkB/BP,EAAAA,OAAO,EAAE,EACP,GAAGE,wBAAWE,kBADP;AAEPC,IAAAA,MAAM,EAAE;AAFD;AAlBsB,CAAlB,CAAf", "sourcesContent": ["// This component is based on RN's DrawerLayoutAndroid API\n// It's cross-compatible with all platforms despite\n// `DrawerLayoutAndroid` only being available on android\n\nimport React, {\n  ReactNode,\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useMemo,\n  useState,\n} from 'react';\n\nimport {\n  StyleSheet,\n  Keyboard,\n  StatusBar,\n  I18nManager,\n  StatusBarAnimation,\n  StyleProp,\n  ViewStyle,\n  LayoutChangeEvent,\n  Platform,\n} from 'react-native';\n\nimport Animated, {\n  Extrapolation,\n  SharedValue,\n  interpolate,\n  runOnJS,\n  useAnimatedProps,\n  useAnimatedStyle,\n  useDerivedValue,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\n\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\nimport {\n  UserSelect,\n  ActiveCursor,\n  MouseButton,\n  HitSlop,\n  GestureStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport { PanGestureHandlerEventPayload } from '../handlers/GestureHandlerEventPayload';\n\nconst DRAG_TOSS = 0.05;\n\nexport enum DrawerPosition {\n  LEFT,\n  RIGHT,\n}\n\nexport enum DrawerState {\n  IDLE,\n  DRAGGING,\n  SETTLING,\n}\n\nexport enum DrawerType {\n  FRONT,\n  BACK,\n  SLIDE,\n}\n\nexport enum DrawerLockMode {\n  UNLOCKED,\n  LOCKED_CLOSED,\n  LOCKED_OPEN,\n}\n\nexport enum DrawerKeyboardDismissMode {\n  NONE,\n  ON_DRAG,\n}\n\nexport interface DrawerLayoutProps {\n  /**\n   * This attribute is present in the native android implementation already and is one\n   * of the required params. The gesture handler version of DrawerLayout makes it\n   * possible for the function passed as `renderNavigationView` to take an\n   * Animated value as a parameter that indicates the progress of drawer\n   * opening/closing animation (progress value is 0 when closed and 1 when\n   * opened). This can be used by the drawer component to animated its children\n   * while the drawer is opening or closing.\n   */\n  renderNavigationView: (\n    progressAnimatedValue: SharedValue<number>\n  ) => ReactNode;\n\n  /**\n   * Determines the side from which the drawer will open.\n   */\n  drawerPosition?: DrawerPosition;\n\n  /**\n   * Width of the drawer.\n   */\n  drawerWidth?: number;\n\n  /**\n   * Background color of the drawer.\n   */\n  drawerBackgroundColor?: string;\n\n  /**\n   * Specifies the lock mode of the drawer.\n   * Programatic opening/closing isn't affected by the lock mode. Defaults to `UNLOCKED`.\n   * - `UNLOCKED` - the drawer will respond to gestures.\n   * - `LOCKED_CLOSED` - the drawer will move freely until it settles in a closed position, then the gestures will be disabled.\n   * - `LOCKED_OPEN` - the drawer will move freely until it settles in an opened position, then the gestures will be disabled.\n   */\n  drawerLockMode?: DrawerLockMode;\n\n  /**\n   * Determines if system keyboard should be closed upon dragging the drawer.\n   */\n  keyboardDismissMode?: DrawerKeyboardDismissMode;\n\n  /**\n   * Called when the drawer is closed.\n   */\n  onDrawerClose?: () => void;\n\n  /**\n   * Called when the drawer is opened.\n   */\n  onDrawerOpen?: () => void;\n\n  /**\n   * Called when the status of the drawer changes.\n   */\n  onDrawerStateChanged?: (\n    newState: DrawerState,\n    drawerWillShow: boolean\n  ) => void;\n\n  /**\n   * Type of animation that will play when opening the drawer.\n   */\n  drawerType?: DrawerType;\n\n  /**\n   * Speed of animation that will play when letting go, or dismissing the drawer.\n   * This will also be the default animation speed for programatic controlls.\n   */\n  animationSpeed?: number;\n\n  /**\n   * Defines how far from the edge of the content view the gesture should\n   * activate.\n   */\n  edgeWidth?: number;\n\n  /**\n   * Minimal distance to swipe before the drawer starts moving.\n   */\n  minSwipeDistance?: number;\n\n  /**\n   * When set to true Drawer component will use\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} API to hide the OS\n   * status bar whenever the drawer is pulled or when its in an \"open\" state.\n   */\n  hideStatusBar?: boolean;\n\n  /**\n   * @default 'slide'\n   *\n   * Can be used when hideStatusBar is set to true and will select the animation\n   * used for hiding/showing the status bar. See\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} documentation for\n   * more details\n   */\n  statusBarAnimation?: StatusBarAnimation;\n\n  /**\n   * @default 'rgba(0, 0, 0, 0.7)'\n   *\n   * Color of the background overlay.\n   * Animated from `0%` to `100%` as the drawer opens.\n   */\n  overlayColor?: string;\n\n  /**\n   * Style wrapping the content.\n   */\n  contentContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style wrapping the drawer.\n   */\n  drawerContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  onDrawerSlide?: (position: number) => void;\n\n  // Implicit `children` prop has been removed in @types/react^18.0.\n  /**\n   * Elements that will be rendered inside the content view.\n   */\n  children?: ReactNode | ((openValue?: SharedValue<number>) => ReactNode);\n\n  /**\n   * @default 'none'\n   * Sets whether the text inside both the drawer and the context window can be selected.\n   * Values: 'none' | 'text' | 'auto'\n   */\n  userSelect?: UserSelect;\n\n  /**\n   * @default 'auto'\n   * Sets the displayed cursor pictogram when the drawer is being dragged.\n   * Values: see CSS cursor values\n   */\n  activeCursor?: ActiveCursor;\n\n  /**\n   * @default 'MouseButton.LEFT'\n   * Allows to choose which mouse button should underlying pan handler react to.\n   */\n  mouseButton?: MouseButton;\n\n  /**\n   * @default 'false if MouseButton.RIGHT is specified'\n   * Allows to enable/disable context menu.\n   */\n  enableContextMenu?: boolean;\n}\n\nexport type DrawerMovementOption = {\n  initialVelocity?: number;\n  animationSpeed?: number;\n};\n\nexport interface DrawerLayoutMethods {\n  openDrawer: (options?: DrawerMovementOption) => void;\n  closeDrawer: (options?: DrawerMovementOption) => void;\n}\n\nconst defaultProps = {\n  drawerWidth: 200,\n  drawerPosition: DrawerPosition.LEFT,\n  drawerType: DrawerType.FRONT,\n  edgeWidth: 20,\n  minSwipeDistance: 3,\n  overlayColor: 'rgba(0, 0, 0, 0.7)',\n  drawerLockMode: DrawerLockMode.UNLOCKED,\n  enableTrackpadTwoFingerGesture: false,\n  activeCursor: 'auto' as ActiveCursor,\n  mouseButton: MouseButton.LEFT,\n  statusBarAnimation: 'slide' as StatusBarAnimation,\n};\n\n// StatusBar.setHidden and Keyboard.dismiss cannot be directly referenced in worklets.\nconst setStatusBarHidden = StatusBar.setHidden;\nconst dismissKeyboard = Keyboard.dismiss;\n\nconst DrawerLayout = forwardRef<DrawerLayoutMethods, DrawerLayoutProps>(\n  function DrawerLayout(props: DrawerLayoutProps, ref) {\n    const [containerWidth, setContainerWidth] = useState(0);\n    const [drawerState, setDrawerState] = useState<DrawerState>(\n      DrawerState.IDLE\n    );\n    const [drawerOpened, setDrawerOpened] = useState(false);\n\n    const {\n      drawerPosition = defaultProps.drawerPosition,\n      drawerWidth = defaultProps.drawerWidth,\n      drawerType = defaultProps.drawerType,\n      drawerBackgroundColor,\n      drawerContainerStyle,\n      contentContainerStyle,\n      minSwipeDistance = defaultProps.minSwipeDistance,\n      edgeWidth = defaultProps.edgeWidth,\n      drawerLockMode = defaultProps.drawerLockMode,\n      overlayColor = defaultProps.overlayColor,\n      enableTrackpadTwoFingerGesture = defaultProps.enableTrackpadTwoFingerGesture,\n      activeCursor = defaultProps.activeCursor,\n      mouseButton = defaultProps.mouseButton,\n      statusBarAnimation = defaultProps.statusBarAnimation,\n      hideStatusBar,\n      keyboardDismissMode,\n      userSelect,\n      enableContextMenu,\n      renderNavigationView,\n      onDrawerSlide,\n      onDrawerClose,\n      onDrawerOpen,\n      onDrawerStateChanged,\n      animationSpeed: animationSpeedProp,\n    } = props;\n\n    const isFromLeft = drawerPosition === DrawerPosition.LEFT;\n\n    const sideCorrection = isFromLeft ? 1 : -1;\n\n    // While closing the drawer when user starts gesture in the greyed out part of the window,\n    // we want the drawer to follow only once the finger reaches the edge of the drawer.\n    // See the diagram for reference. * = starting finger position, < = current finger position\n    // 1) +---------------+ 2) +---------------+ 3) +---------------+ 4) +---------------+\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|..<*..|    |XXXXXXXX|.<-*..|    |XXXXXXXX|<--*..|    |XXXXX|<-----*..|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    +---------------+    +---------------+    +---------------+    +---------------+\n\n    const openValue = useSharedValue<number>(0);\n\n    useDerivedValue(() => {\n      onDrawerSlide && runOnJS(onDrawerSlide)(openValue.value);\n    }, []);\n\n    const isDrawerOpen = useSharedValue(false);\n\n    const handleContainerLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n      setContainerWidth(nativeEvent.layout.width);\n    };\n\n    const emitStateChanged = useCallback(\n      (newState: DrawerState, drawerWillShow: boolean) => {\n        'worklet';\n        onDrawerStateChanged &&\n          runOnJS(onDrawerStateChanged)?.(newState, drawerWillShow);\n      },\n      [onDrawerStateChanged]\n    );\n\n    const drawerAnimatedProps = useAnimatedProps(() => ({\n      accessibilityViewIsModal: isDrawerOpen.value,\n    }));\n\n    const overlayAnimatedProps = useAnimatedProps(() => ({\n      pointerEvents: isDrawerOpen.value ? ('auto' as const) : ('none' as const),\n    }));\n\n    // While the drawer is hidden, it's hitSlop overflows onto the main view by edgeWidth\n    // This way it can be swiped open even when it's hidden\n    const [edgeHitSlop, setEdgeHitSlop] = useState<HitSlop>(\n      isFromLeft\n        ? { left: 0, width: edgeWidth }\n        : { right: 0, width: edgeWidth }\n    );\n\n    // gestureOrientation is 1 if the gesture is expected to move from left to right and -1 otherwise\n    const gestureOrientation = useMemo(\n      () => sideCorrection * (drawerOpened ? -1 : 1),\n      [sideCorrection, drawerOpened]\n    );\n\n    useEffect(() => {\n      setEdgeHitSlop(\n        isFromLeft\n          ? { left: 0, width: edgeWidth }\n          : { right: 0, width: edgeWidth }\n      );\n    }, [isFromLeft, edgeWidth]);\n\n    const animateDrawer = useCallback(\n      (toValue: number, initialVelocity: number, animationSpeed?: number) => {\n        'worklet';\n        const willShow = toValue !== 0;\n        isDrawerOpen.value = willShow;\n\n        emitStateChanged(DrawerState.SETTLING, willShow);\n        runOnJS(setDrawerState)(DrawerState.SETTLING);\n\n        if (hideStatusBar) {\n          runOnJS(setStatusBarHidden)(willShow, statusBarAnimation);\n        }\n\n        const normalizedToValue = interpolate(\n          toValue,\n          [0, drawerWidth],\n          [0, 1],\n          Extrapolation.CLAMP\n        );\n\n        const normalizedInitialVelocity = interpolate(\n          initialVelocity,\n          [0, drawerWidth],\n          [0, 1],\n          Extrapolation.CLAMP\n        );\n\n        openValue.value = withSpring(\n          normalizedToValue,\n          {\n            overshootClamping: true,\n            velocity: normalizedInitialVelocity,\n            mass: animationSpeed\n              ? 1 / animationSpeed\n              : 1 / (animationSpeedProp ?? 1),\n            damping: 40,\n            stiffness: 500,\n          },\n          (finished) => {\n            if (finished) {\n              emitStateChanged(DrawerState.IDLE, willShow);\n              runOnJS(setDrawerOpened)(willShow);\n              runOnJS(setDrawerState)(DrawerState.IDLE);\n              if (willShow) {\n                onDrawerOpen && runOnJS(onDrawerOpen)?.();\n              } else {\n                onDrawerClose && runOnJS(onDrawerClose)?.();\n              }\n            }\n          }\n        );\n      },\n      [\n        openValue,\n        emitStateChanged,\n        isDrawerOpen,\n        hideStatusBar,\n        onDrawerClose,\n        onDrawerOpen,\n        drawerWidth,\n        statusBarAnimation,\n      ]\n    );\n\n    const handleRelease = useCallback(\n      (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n        'worklet';\n        let { translationX: dragX, velocityX, x: touchX } = event;\n\n        if (drawerPosition !== DrawerPosition.LEFT) {\n          // See description in _updateAnimatedEvent about why events are flipped\n          // for right-side drawer\n          dragX = -dragX;\n          touchX = containerWidth - touchX;\n          velocityX = -velocityX;\n        }\n\n        const gestureStartX = touchX - dragX;\n        let dragOffsetBasedOnStart = 0;\n\n        if (drawerType === DrawerType.FRONT) {\n          dragOffsetBasedOnStart =\n            gestureStartX > drawerWidth ? gestureStartX - drawerWidth : 0;\n        }\n\n        const startOffsetX =\n          dragX +\n          dragOffsetBasedOnStart +\n          (isDrawerOpen.value ? drawerWidth : 0);\n\n        const projOffsetX = startOffsetX + DRAG_TOSS * velocityX;\n\n        const shouldOpen = projOffsetX > drawerWidth / 2;\n\n        if (shouldOpen) {\n          animateDrawer(drawerWidth, velocityX);\n        } else {\n          animateDrawer(0, velocityX);\n        }\n      },\n      [\n        animateDrawer,\n        containerWidth,\n        drawerPosition,\n        drawerType,\n        drawerWidth,\n        isDrawerOpen,\n      ]\n    );\n\n    const openDrawer = useCallback(\n      (options: DrawerMovementOption = {}) => {\n        'worklet';\n        animateDrawer(\n          drawerWidth,\n          options.initialVelocity ?? 0,\n          options.animationSpeed\n        );\n      },\n      [animateDrawer, drawerWidth]\n    );\n\n    const closeDrawer = useCallback(\n      (options: DrawerMovementOption = {}) => {\n        'worklet';\n        animateDrawer(0, options.initialVelocity ?? 0, options.animationSpeed);\n      },\n      [animateDrawer]\n    );\n\n    const overlayDismissGesture = useMemo(\n      () =>\n        Gesture.Tap()\n          .maxDistance(25)\n          .onEnd(() => {\n            if (\n              isDrawerOpen.value &&\n              drawerLockMode !== DrawerLockMode.LOCKED_OPEN\n            ) {\n              closeDrawer();\n            }\n          }),\n      [closeDrawer, isDrawerOpen, drawerLockMode]\n    );\n\n    const overlayAnimatedStyle = useAnimatedStyle(() => ({\n      opacity: openValue.value,\n      backgroundColor: overlayColor,\n    }));\n\n    const fillHitSlop = useMemo(\n      () => (isFromLeft ? { left: drawerWidth } : { right: drawerWidth }),\n      [drawerWidth, isFromLeft]\n    );\n\n    const panGesture = useMemo(() => {\n      return Gesture.Pan()\n        .activeCursor(activeCursor)\n        .mouseButton(mouseButton)\n        .hitSlop(drawerOpened ? fillHitSlop : edgeHitSlop)\n        .minDistance(drawerOpened ? 100 : 0)\n        .activeOffsetX(gestureOrientation * minSwipeDistance)\n        .failOffsetY([-15, 15])\n        .simultaneousWithExternalGesture(overlayDismissGesture)\n        .enableTrackpadTwoFingerGesture(enableTrackpadTwoFingerGesture)\n        .enabled(\n          drawerState !== DrawerState.SETTLING &&\n            (drawerOpened\n              ? drawerLockMode !== DrawerLockMode.LOCKED_OPEN\n              : drawerLockMode !== DrawerLockMode.LOCKED_CLOSED)\n        )\n        .onStart(() => {\n          emitStateChanged(DrawerState.DRAGGING, false);\n          runOnJS(setDrawerState)(DrawerState.DRAGGING);\n          if (keyboardDismissMode === DrawerKeyboardDismissMode.ON_DRAG) {\n            runOnJS(dismissKeyboard)();\n          }\n          if (hideStatusBar) {\n            runOnJS(setStatusBarHidden)(true, statusBarAnimation);\n          }\n        })\n        .onUpdate((event) => {\n          const startedOutsideTranslation = isFromLeft\n            ? interpolate(\n                event.x,\n                [0, drawerWidth, drawerWidth + 1],\n                [0, drawerWidth, drawerWidth]\n              )\n            : interpolate(\n                event.x - containerWidth,\n                [-drawerWidth - 1, -drawerWidth, 0],\n                [drawerWidth, drawerWidth, 0]\n              );\n\n          const startedInsideTranslation =\n            sideCorrection *\n            (event.translationX +\n              (drawerOpened ? drawerWidth * -gestureOrientation : 0));\n\n          const adjustedTranslation = Math.max(\n            drawerOpened ? startedOutsideTranslation : 0,\n            startedInsideTranslation\n          );\n\n          openValue.value = interpolate(\n            adjustedTranslation,\n            [-drawerWidth, 0, drawerWidth],\n            [1, 0, 1],\n            Extrapolation.CLAMP\n          );\n        })\n        .onEnd(handleRelease);\n    }, [\n      drawerLockMode,\n      openValue,\n      drawerWidth,\n      emitStateChanged,\n      gestureOrientation,\n      handleRelease,\n      edgeHitSlop,\n      fillHitSlop,\n      minSwipeDistance,\n      hideStatusBar,\n      keyboardDismissMode,\n      overlayDismissGesture,\n      drawerOpened,\n      isFromLeft,\n      containerWidth,\n      sideCorrection,\n      drawerState,\n      activeCursor,\n      enableTrackpadTwoFingerGesture,\n      mouseButton,\n      statusBarAnimation,\n    ]);\n\n    // When using RTL, row and row-reverse flex directions are flipped.\n    const reverseContentDirection = I18nManager.isRTL\n      ? isFromLeft\n      : !isFromLeft;\n\n    const dynamicDrawerStyles = {\n      backgroundColor: drawerBackgroundColor,\n      width: drawerWidth,\n    };\n\n    const containerStyles = useAnimatedStyle(() => {\n      if (drawerType === DrawerType.FRONT) {\n        return {};\n      }\n\n      return {\n        transform: [\n          {\n            translateX: interpolate(\n              openValue.value,\n              [0, 1],\n              [0, drawerWidth * sideCorrection],\n              Extrapolation.CLAMP\n            ),\n          },\n        ],\n      };\n    });\n\n    const drawerAnimatedStyle = useAnimatedStyle(() => {\n      const closedDrawerOffset = drawerWidth * -sideCorrection;\n      const isBack = drawerType === DrawerType.BACK;\n      const isIdle = drawerState === DrawerState.IDLE;\n\n      if (isBack) {\n        return {\n          transform: [{ translateX: 0 }],\n          flexDirection: reverseContentDirection ? 'row-reverse' : 'row',\n        };\n      }\n\n      let translateX = 0;\n\n      if (isIdle) {\n        translateX = drawerOpened ? 0 : closedDrawerOffset;\n      } else {\n        translateX = interpolate(\n          openValue.value,\n          [0, 1],\n          [closedDrawerOffset, 0],\n          Extrapolation.CLAMP\n        );\n      }\n\n      return {\n        transform: [{ translateX }],\n        flexDirection: reverseContentDirection ? 'row-reverse' : 'row',\n      };\n    });\n\n    const containerAnimatedProps = useAnimatedProps(() => ({\n      importantForAccessibility:\n        Platform.OS === 'android'\n          ? isDrawerOpen.value\n            ? ('no-hide-descendants' as const)\n            : ('yes' as const)\n          : undefined,\n    }));\n\n    const children =\n      typeof props.children === 'function'\n        ? props.children(openValue) // renderer function\n        : props.children;\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        openDrawer,\n        closeDrawer,\n      }),\n      [openDrawer, closeDrawer]\n    );\n\n    return (\n      <GestureDetector\n        gesture={panGesture}\n        userSelect={userSelect}\n        enableContextMenu={enableContextMenu}>\n        <Animated.View style={styles.main} onLayout={handleContainerLayout}>\n          <GestureDetector gesture={overlayDismissGesture}>\n            <Animated.View\n              style={[\n                drawerType === DrawerType.FRONT\n                  ? styles.containerOnBack\n                  : styles.containerInFront,\n                containerStyles,\n                contentContainerStyle,\n              ]}\n              animatedProps={containerAnimatedProps}>\n              {children}\n              <Animated.View\n                animatedProps={overlayAnimatedProps}\n                style={[styles.overlay, overlayAnimatedStyle]}\n              />\n            </Animated.View>\n          </GestureDetector>\n          <Animated.View\n            pointerEvents=\"box-none\"\n            animatedProps={drawerAnimatedProps}\n            style={[\n              styles.drawerContainer,\n              drawerAnimatedStyle,\n              drawerContainerStyle,\n            ]}>\n            <Animated.View style={dynamicDrawerStyles}>\n              {renderNavigationView(openValue)}\n            </Animated.View>\n          </Animated.View>\n        </Animated.View>\n      </GestureDetector>\n    );\n  }\n);\n\nexport default DrawerLayout;\n\nconst styles = StyleSheet.create({\n  drawerContainer: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1001,\n    flexDirection: 'row',\n  },\n  containerInFront: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1002,\n  },\n  containerOnBack: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  main: {\n    flex: 1,\n    zIndex: 0,\n    overflow: 'hidden',\n  },\n  overlay: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1000,\n  },\n});\n"]}