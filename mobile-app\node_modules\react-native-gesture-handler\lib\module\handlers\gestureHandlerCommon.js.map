{"version": 3, "sources": ["gestureHandlerCommon.ts"], "names": ["commonProps", "componentInteractionProps", "baseGestureHandlerProps", "baseGestureHandlerWithDetectorProps", "MouseB<PERSON>on"], "mappings": "AAAA;AACA;AACA;AACA;AAQA,MAAMA,WAAW,GAAG,CAClB,IADkB,EAElB,SAFkB,EAGlB,yBAHkB,EAIlB,SAJkB,EAKlB,sBALkB,EAMlB,YANkB,EAOlB,cAPkB,EAQlB,aARkB,EASlB,mBATkB,EAUlB,aAVkB,CAApB;AAaA,MAAMC,yBAAyB,GAAG,CAChC,SADgC,EAEhC,sBAFgC,EAGhC,gBAHgC,CAAlC;AAMA,OAAO,MAAMC,uBAAuB,GAAG,CACrC,GAAGF,WADkC,EAErC,GAAGC,yBAFkC,EAGrC,SAHqC,EAIrC,UAJqC,EAKrC,aALqC,EAMrC,aANqC,EAOrC,SAPqC,EAQrC,gBARqC,EASrC,sBATqC,CAAhC;AAYP,OAAO,MAAME,mCAAmC,GAAG,CACjD,GAAGH,WAD8C,EAEjD,kBAFiD,EAGjD,kBAHiD,CAA5C;AAsEP,WAAYI,WAAZ;;WAAYA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;GAAAA,W,KAAAA,W", "sourcesContent": ["// Previous types exported gesture handlers as classes which creates an interface and variable, both named the same as class.\n// Without those types, we'd introduce breaking change, forcing users to prefix every handler type specification with typeof\n// e.g. React.createRef<TapGestureHandler> -> React.createRef<typeof TapGestureHandler>.\n// See https://www.typescriptlang.org/docs/handbook/classes.html#constructor-functions for reference.\nimport * as React from 'react';\n\nimport { State } from '../State';\nimport { TouchEventType } from '../TouchEventType';\nimport { ValueOf } from '../typeUtils';\nimport { PointerType } from '../PointerType';\n\nconst commonProps = [\n  'id',\n  'enabled',\n  'shouldCancelWhenOutside',\n  'hitSlop',\n  'cancelsTouchesInView',\n  'userSelect',\n  'activeCursor',\n  'mouseButton',\n  'enableContextMenu',\n  'touchAction',\n] as const;\n\nconst componentInteractionProps = [\n  'waitFor',\n  'simultaneousHandlers',\n  'blocksHandlers',\n] as const;\n\nexport const baseGestureHandlerProps = [\n  ...commonProps,\n  ...componentInteractionProps,\n  'onBegan',\n  'onFailed',\n  'onCancelled',\n  'onActivated',\n  'onEnded',\n  'onGestureEvent',\n  'onHandlerStateChange',\n] as const;\n\nexport const baseGestureHandlerWithDetectorProps = [\n  ...commonProps,\n  'needsPointerData',\n  'manualActivation',\n];\n\nexport interface GestureEventPayload {\n  handlerTag: number;\n  numberOfPointers: number;\n  state: ValueOf<typeof State>;\n  pointerType: PointerType;\n}\nexport interface HandlerStateChangeEventPayload extends GestureEventPayload {\n  oldState: ValueOf<typeof State>;\n}\n\nexport type HitSlop =\n  | number\n  | null\n  | undefined\n  | Partial<\n      Record<\n        'left' | 'right' | 'top' | 'bottom' | 'vertical' | 'horizontal',\n        number\n      >\n    >\n  | Record<'width' | 'left', number>\n  | Record<'width' | 'right', number>\n  | Record<'height' | 'top', number>\n  | Record<'height' | 'bottom', number>;\n\nexport type UserSelect = 'none' | 'auto' | 'text';\nexport type ActiveCursor =\n  | 'auto'\n  | 'default'\n  | 'none'\n  | 'context-menu'\n  | 'help'\n  | 'pointer'\n  | 'progress'\n  | 'wait'\n  | 'cell'\n  | 'crosshair'\n  | 'text'\n  | 'vertical-text'\n  | 'alias'\n  | 'copy'\n  | 'move'\n  | 'no-drop'\n  | 'not-allowed'\n  | 'grab'\n  | 'grabbing'\n  | 'e-resize'\n  | 'n-resize'\n  | 'ne-resize'\n  | 'nw-resize'\n  | 's-resize'\n  | 'se-resize'\n  | 'sw-resize'\n  | 'w-resize'\n  | 'ew-resize'\n  | 'ns-resize'\n  | 'nesw-resize'\n  | 'nwse-resize'\n  | 'col-resize'\n  | 'row-resize'\n  | 'all-scroll'\n  | 'zoom-in'\n  | 'zoom-out';\n\nexport enum MouseButton {\n  LEFT = 1,\n  RIGHT = 2,\n  MIDDLE = 4,\n  BUTTON_4 = 8,\n  BUTTON_5 = 16,\n  ALL = 31,\n}\n\nexport type TouchAction =\n  | 'auto'\n  | 'none'\n  | 'pan-x'\n  | 'pan-left'\n  | 'pan-right'\n  | 'pan-y'\n  | 'pan-up'\n  | 'pan-down'\n  | 'pinch-zoom'\n  | 'manipulation'\n  | 'inherit'\n  | 'initial'\n  | 'revert'\n  | 'revert-layer'\n  | 'unset';\n\n// TODO(TS) events in handlers\n\nexport interface GestureEvent<ExtraEventPayloadT = Record<string, unknown>> {\n  nativeEvent: Readonly<GestureEventPayload & ExtraEventPayloadT>;\n}\nexport interface HandlerStateChangeEvent<\n  ExtraEventPayloadT = Record<string, unknown>,\n> {\n  nativeEvent: Readonly<HandlerStateChangeEventPayload & ExtraEventPayloadT>;\n}\n\nexport type TouchData = {\n  id: number;\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n};\n\nexport type GestureTouchEvent = {\n  handlerTag: number;\n  numberOfTouches: number;\n  state: ValueOf<typeof State>;\n  eventType: TouchEventType;\n  allTouches: TouchData[];\n  changedTouches: TouchData[];\n  pointerType: PointerType;\n};\n\nexport type GestureUpdateEvent<GestureEventPayloadT = Record<string, unknown>> =\n  GestureEventPayload & GestureEventPayloadT;\n\nexport type GestureStateChangeEvent<\n  GestureStateChangeEventPayloadT = Record<string, unknown>,\n> = HandlerStateChangeEventPayload & GestureStateChangeEventPayloadT;\n\nexport type CommonGestureConfig = {\n  enabled?: boolean;\n  shouldCancelWhenOutside?: boolean;\n  hitSlop?: HitSlop;\n  userSelect?: UserSelect;\n  activeCursor?: ActiveCursor;\n  mouseButton?: MouseButton;\n  enableContextMenu?: boolean;\n  touchAction?: TouchAction;\n};\n\n// Events payloads are types instead of interfaces due to TS limitation.\n// See https://github.com/microsoft/TypeScript/issues/15300 for more info.\nexport type BaseGestureHandlerProps<\n  ExtraEventPayloadT extends Record<string, unknown> = Record<string, unknown>,\n> = CommonGestureConfig & {\n  id?: string;\n  waitFor?: React.Ref<unknown> | React.Ref<unknown>[];\n  simultaneousHandlers?: React.Ref<unknown> | React.Ref<unknown>[];\n  blocksHandlers?: React.Ref<unknown> | React.Ref<unknown>[];\n  testID?: string;\n  cancelsTouchesInView?: boolean;\n  // TODO(TS) - fix event types\n  onBegan?: (event: HandlerStateChangeEvent) => void;\n  onFailed?: (event: HandlerStateChangeEvent) => void;\n  onCancelled?: (event: HandlerStateChangeEvent) => void;\n  onActivated?: (event: HandlerStateChangeEvent) => void;\n  onEnded?: (event: HandlerStateChangeEvent) => void;\n\n  // TODO(TS) consider using NativeSyntheticEvent\n  onGestureEvent?: (event: GestureEvent<ExtraEventPayloadT>) => void;\n  onHandlerStateChange?: (\n    event: HandlerStateChangeEvent<ExtraEventPayloadT>\n  ) => void;\n  // Implicit `children` prop has been removed in @types/react^18.0.0\n  children?: React.ReactNode;\n};\n"]}