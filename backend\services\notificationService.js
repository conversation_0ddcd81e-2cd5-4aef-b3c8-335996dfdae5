const admin = require('firebase-admin');
const nodemailer = require('nodemailer');

// Configuration Firebase Admin SDK
const initializeFirebase = () => {
  if (!admin.apps.length) {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: process.env.FIREBASE_AUTH_URI,
      token_uri: process.env.FIREBASE_TOKEN_URI,
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });
  }
};

// Configuration Nodemailer
const createEmailTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

class NotificationService {
  constructor() {
    this.initializeServices();
  }

  initializeServices() {
    try {
      initializeFirebase();
      this.emailTransporter = createEmailTransporter();
      console.log('✅ Services de notification initialisés');
    } catch (error) {
      console.error('❌ Erreur initialisation notifications:', error.message);
    }
  }

  // Envoyer une notification push à un utilisateur
  async sendPushNotification(fcmToken, title, body, data = {}) {
    try {
      if (!fcmToken) {
        console.log('⚠️ Token FCM manquant');
        return null;
      }

      const message = {
        token: fcmToken,
        notification: {
          title,
          body
        },
        data: {
          ...data,
          timestamp: new Date().toISOString()
        },
        android: {
          priority: 'high',
          notification: {
            sound: 'default',
            channelId: 'grosmarket_notifications'
          }
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1
            }
          }
        }
      };

      const response = await admin.messaging().send(message);
      console.log('✅ Notification push envoyée:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur envoi notification push:', error.message);
      return null;
    }
  }

  // Envoyer des notifications push à plusieurs utilisateurs
  async sendMulticastNotification(fcmTokens, title, body, data = {}) {
    try {
      const validTokens = fcmTokens.filter(token => token && token.trim());
      
      if (validTokens.length === 0) {
        console.log('⚠️ Aucun token FCM valide');
        return null;
      }

      const message = {
        tokens: validTokens,
        notification: {
          title,
          body
        },
        data: {
          ...data,
          timestamp: new Date().toISOString()
        },
        android: {
          priority: 'high',
          notification: {
            sound: 'default',
            channelId: 'grosmarket_notifications'
          }
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1
            }
          }
        }
      };

      const response = await admin.messaging().sendMulticast(message);
      console.log(`✅ Notifications envoyées: ${response.successCount}/${validTokens.length}`);
      
      // Gérer les tokens invalides
      if (response.failureCount > 0) {
        const failedTokens = [];
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            failedTokens.push(validTokens[idx]);
            console.log('❌ Token FCM invalide:', validTokens[idx]);
          }
        });
        return { success: response.successCount, failed: failedTokens };
      }

      return { success: response.successCount, failed: [] };
    } catch (error) {
      console.error('❌ Erreur envoi notifications multiples:', error.message);
      return null;
    }
  }

  // Envoyer un email
  async sendEmail(to, subject, html, text = null) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to,
        subject,
        html,
        text: text || html.replace(/<[^>]*>/g, '') // Fallback text sans HTML
      };

      const result = await this.emailTransporter.sendMail(mailOptions);
      console.log('✅ Email envoyé:', result.messageId);
      return result;
    } catch (error) {
      console.error('❌ Erreur envoi email:', error.message);
      return null;
    }
  }

  // Notifications spécifiques pour les commandes
  async notifyOrderStatusChange(client, order, newStatus) {
    const statusMessages = {
      confirmee: {
        title: 'Commande confirmée',
        body: `Votre commande ${order.numeroCommande} a été confirmée`
      },
      en_preparation: {
        title: 'Commande en préparation',
        body: `Votre commande ${order.numeroCommande} est en cours de préparation`
      },
      en_livraison: {
        title: 'Commande en livraison',
        body: `Votre commande ${order.numeroCommande} est en cours de livraison`
      },
      livree: {
        title: 'Commande livrée',
        body: `Votre commande ${order.numeroCommande} a été livrée avec succès`
      },
      annulee: {
        title: 'Commande annulée',
        body: `Votre commande ${order.numeroCommande} a été annulée`
      }
    };

    const message = statusMessages[newStatus];
    if (message && client.fcmToken) {
      await this.sendPushNotification(
        client.fcmToken,
        message.title,
        message.body,
        {
          type: 'order_status',
          orderId: order._id.toString(),
          orderNumber: order.numeroCommande,
          status: newStatus
        }
      );
    }
  }

  // Notification pour stock faible
  async notifyLowStock(product, admins) {
    const title = 'Stock faible';
    const body = `Le produit "${product.nom}" a un stock faible (${product.stock} ${product.unite})`;
    
    // Notification push aux admins
    const adminTokens = admins
      .filter(admin => admin.fcmToken)
      .map(admin => admin.fcmToken);
    
    if (adminTokens.length > 0) {
      await this.sendMulticastNotification(
        adminTokens,
        title,
        body,
        {
          type: 'low_stock',
          productId: product._id.toString(),
          productName: product.nom,
          currentStock: product.stock.toString()
        }
      );
    }

    // Email aux admins
    const adminEmails = admins.map(admin => admin.email);
    if (adminEmails.length > 0) {
      const emailHtml = `
        <h2>Alerte Stock Faible</h2>
        <p>Le produit <strong>${product.nom}</strong> a un stock faible.</p>
        <ul>
          <li>Stock actuel: ${product.stock} ${product.unite}</li>
          <li>Seuil d'alerte: ${product.seuilAlerte} ${product.unite}</li>
          <li>Catégorie: ${product.categorie}</li>
        </ul>
        <p>Veuillez réapprovisionner ce produit rapidement.</p>
      `;

      for (const email of adminEmails) {
        await this.sendEmail(email, title, emailHtml);
      }
    }
  }

  // Notification pour nouvelle commande
  async notifyNewOrder(order, admins) {
    const title = 'Nouvelle commande';
    const body = `Nouvelle commande ${order.numeroCommande} de ${order.montantTotal}€`;
    
    // Notification push aux admins
    const adminTokens = admins
      .filter(admin => admin.fcmToken)
      .map(admin => admin.fcmToken);
    
    if (adminTokens.length > 0) {
      await this.sendMulticastNotification(
        adminTokens,
        title,
        body,
        {
          type: 'new_order',
          orderId: order._id.toString(),
          orderNumber: order.numeroCommande,
          amount: order.montantTotal.toString()
        }
      );
    }
  }
}

module.exports = new NotificationService();
