{"version": 3, "sources": ["interfaces.ts"], "names": ["EventTypes", "TouchEventType", "WheelDevice"], "mappings": ";;;;;;IA6JYA,U;;;WAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;AAAAA,EAAAA,U,CAAAA,U;GAAAA,U,0BAAAA,U;;IAWAC,c;;;WAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;AAAAA,EAAAA,c,CAAAA,c;GAAAA,c,8BAAAA,c;;IAQAC,W;;;WAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;AAAAA,EAAAA,W,CAAAA,W;GAAAA,W,2BAAAA,W", "sourcesContent": ["import {\n  UserSelect,\n  ActiveCursor,\n  MouseButton,\n  TouchAction,\n} from '../handlers/gestureHandlerCommon';\nimport { Directions } from '../Directions';\nimport { State } from '../State';\nimport { PointerType } from '../PointerType';\n\nexport interface HitSlop {\n  left?: number;\n  right?: number;\n  top?: number;\n  bottom?: number;\n  horizontal?: number;\n  vertical?: number;\n  width?: number;\n  height?: number;\n}\n\nexport interface Handler {\n  handlerTag: number;\n}\n\ntype ConfigArgs =\n  | number\n  | boolean\n  | HitSlop\n  | UserSelect\n  | TouchAction\n  | ActiveCursor\n  | Directions\n  | Handler[]\n  | null\n  | undefined;\n\nexport interface Config extends Record<string, ConfigArgs> {\n  enabled: boolean;\n  simultaneousHandlers?: Handler[] | null;\n  waitFor?: Handler[] | null;\n  blocksHandlers?: Handler[] | null;\n  hitSlop?: HitSlop;\n  shouldCancelWhenOutside?: boolean;\n  userSelect?: UserSelect;\n  activeCursor?: ActiveCursor;\n  mouseButton?: MouseButton;\n  enableContextMenu?: boolean;\n  touchAction?: TouchAction;\n  manualActivation?: boolean;\n\n  activateAfterLongPress?: number;\n  failOffsetXStart?: number;\n  failOffsetYStart?: number;\n  failOffsetXEnd?: number;\n  failOffsetYEnd?: number;\n  activeOffsetXStart?: number;\n  activeOffsetXEnd?: number;\n  activeOffsetYStart?: number;\n  activeOffsetYEnd?: number;\n  minPointers?: number;\n  maxPointers?: number;\n  minDist?: number;\n  minDistSq?: number;\n  minVelocity?: number;\n  minVelocityX?: number;\n  minVelocityY?: number;\n  minVelocitySq?: number;\n  maxDist?: number;\n  maxDistSq?: number;\n  numberOfPointers?: number;\n  minDurationMs?: number;\n  numberOfTaps?: number;\n  maxDurationMs?: number;\n  maxDelayMs?: number;\n  maxDeltaX?: number;\n  maxDeltaY?: number;\n  shouldActivateOnStart?: boolean;\n  disallowInterruption?: boolean;\n  direction?: Directions;\n  enableTrackpadTwoFingerGesture?: boolean;\n}\n\ntype NativeEventArgs = number | State | boolean | undefined;\ninterface NativeEvent extends Record<string, NativeEventArgs> {\n  numberOfPointers: number;\n  state: State;\n  pointerInside: boolean | undefined;\n  handlerTag: number;\n  target: number;\n  oldState?: State;\n  pointerType: PointerType;\n}\n\nexport interface Point {\n  x: number;\n  y: number;\n}\n\nexport interface PointerData {\n  id: number;\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n}\n\ntype TouchNativeArgs = number | State | TouchEventType | PointerData[];\n\ninterface NativeTouchEvent extends Record<string, TouchNativeArgs> {\n  handlerTag: number;\n  state: State;\n  eventType: TouchEventType;\n  changedTouches: PointerData[];\n  allTouches: PointerData[];\n  numberOfTouches: number;\n  pointerType: PointerType;\n}\n\nexport interface ResultEvent extends Record<string, NativeEvent | number> {\n  nativeEvent: NativeEvent;\n  timeStamp: number;\n}\n\nexport interface ResultTouchEvent\n  extends Record<string, NativeTouchEvent | number> {\n  nativeEvent: NativeTouchEvent;\n  timeStamp: number;\n}\n\nexport interface PropsRef {\n  onGestureHandlerEvent: () => void;\n  onGestureHandlerStateChange: () => void;\n}\n\nexport interface StylusData {\n  tiltX: number;\n  tiltY: number;\n  azimuthAngle: number;\n  altitudeAngle: number;\n  pressure: number;\n}\n\nexport interface AdaptedEvent {\n  x: number;\n  y: number;\n  offsetX: number;\n  offsetY: number;\n  pointerId: number;\n  eventType: EventTypes;\n  pointerType: PointerType;\n  time: number;\n  button?: MouseButton;\n  stylusData?: StylusData;\n  wheelDeltaY?: number;\n}\n\nexport enum EventTypes {\n  DOWN,\n  ADDITIONAL_POINTER_DOWN,\n  UP,\n  ADDITIONAL_POINTER_UP,\n  MOVE,\n  ENTER,\n  LEAVE,\n  CANCEL,\n}\n\nexport enum TouchEventType {\n  UNDETERMINED,\n  DOWN,\n  MOVE,\n  UP,\n  CANCELLED,\n}\n\nexport enum WheelDevice {\n  UNDETERMINED,\n  MOUSE,\n  TOUCHPAD,\n}\n\nexport type GestureHandlerRef = {\n  viewTag: GestureHandlerRef;\n  current: HTMLElement;\n};\n\nexport type SVGRef = {\n  elementRef: { current: SVGElement };\n};\n"]}