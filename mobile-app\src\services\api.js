import axios from 'axios';
import { Platform } from 'react-native';
import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES, STORAGE_KEYS } from '../constants';

// Import conditionnel d'AsyncStorage
let AsyncStorage;
if (Platform.OS === 'web') {
  // Pour le web, utiliser localStorage
  AsyncStorage = {
    getItem: async (key) => {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        return null;
      }
    },
    setItem: async (key, value) => {
      try {
        localStorage.setItem(key, value);
        return true;
      } catch (error) {
        return false;
      }
    },
    removeItem: async (key) => {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        return false;
      }
    },
    multiRemove: async (keys) => {
      try {
        keys.forEach(key => localStorage.removeItem(key));
        return true;
      } catch (error) {
        return false;
      }
    }
  };
} else {
  // Pour mobile, utiliser AsyncStorage normal
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
}

// Configuration de l'instance Axios
const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur de requête pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn('Erreur lors de la récupération du token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur de réponse pour gérer les erreurs globalement
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const { response } = error;
    
    // Gestion des erreurs d'authentification
    if (response?.status === HTTP_STATUS.UNAUTHORIZED) {
      // Token expiré, supprimer les données d'authentification
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.USER_TYPE,
      ]);
      
      // Rediriger vers l'écran de connexion
      // Cette logique sera gérée par le contexte d'authentification
    }
    
    return Promise.reject(error);
  }
);

// Classe de service API
class ApiService {
  
  // Méthode générique pour les requêtes GET
  async get(endpoint, params = {}) {
    try {
      const response = await apiClient.get(endpoint, { params });
      return { success: true, data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Méthode générique pour les requêtes POST
  async post(endpoint, data = {}) {
    try {
      const response = await apiClient.post(endpoint, data);
      return { success: true, data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Méthode générique pour les requêtes PUT
  async put(endpoint, data = {}) {
    try {
      const response = await apiClient.put(endpoint, data);
      return { success: true, data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Méthode générique pour les requêtes DELETE
  async delete(endpoint) {
    try {
      const response = await apiClient.delete(endpoint);
      return { success: true, data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Gestion des erreurs
  handleError(error) {
    let message = ERROR_MESSAGES.UNKNOWN;
    let statusCode = null;

    if (error.response) {
      // Erreur de réponse du serveur
      statusCode = error.response.status;
      
      switch (statusCode) {
        case HTTP_STATUS.BAD_REQUEST:
          message = error.response.data?.message || 'Données invalides';
          break;
        case HTTP_STATUS.UNAUTHORIZED:
          message = ERROR_MESSAGES.UNAUTHORIZED;
          break;
        case HTTP_STATUS.FORBIDDEN:
          message = ERROR_MESSAGES.FORBIDDEN;
          break;
        case HTTP_STATUS.NOT_FOUND:
          message = ERROR_MESSAGES.NOT_FOUND;
          break;
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
          message = ERROR_MESSAGES.SERVER_ERROR;
          break;
        default:
          message = error.response.data?.message || ERROR_MESSAGES.UNKNOWN;
      }
    } else if (error.request) {
      // Erreur de réseau
      message = ERROR_MESSAGES.NETWORK_ERROR;
    } else if (error.code === 'ECONNABORTED') {
      // Timeout
      message = ERROR_MESSAGES.TIMEOUT;
    }

    console.error('API Error:', error);

    return {
      success: false,
      error: {
        message,
        statusCode,
        originalError: error,
      },
    };
  }

  // Méthodes d'authentification
  async registerClient(userData) {
    return this.post('/auth/register/client', userData);
  }

  async loginClient(credentials) {
    return this.post('/auth/login/client', credentials);
  }

  async loginAdmin(credentials) {
    return this.post('/auth/login/admin', credentials);
  }

  async getProfile() {
    return this.get('/auth/profile');
  }

  async updateFCMToken(fcmToken) {
    return this.put('/auth/fcm-token', { fcmToken });
  }

  async changePassword(passwordData) {
    return this.put('/auth/change-password', passwordData);
  }

  async logout() {
    return this.post('/auth/logout');
  }

  // Méthodes pour les produits
  async getProducts(params = {}) {
    return this.get('/products', params);
  }

  async getProductById(id) {
    return this.get(`/products/${id}`);
  }

  async getCategories() {
    return this.get('/products/meta/categories');
  }

  async getPromotionalProducts(params = {}) {
    return this.get('/products/special/promotions', params);
  }

  async searchProducts(query, params = {}) {
    return this.get('/products/search/query', { q: query, ...params });
  }

  async getSimilarProducts(id, limit = 5) {
    return this.get(`/products/${id}/similar`, { limit });
  }

  async checkProductAvailability(id, quantity = 1) {
    return this.get(`/products/${id}/availability`, { quantite: quantity });
  }

  // Méthodes pour les commandes
  async createOrder(orderData) {
    return this.post('/orders', orderData);
  }

  async getOrders(params = {}) {
    return this.get('/orders', params);
  }

  async getOrderById(id) {
    return this.get(`/orders/${id}`);
  }

  async getOrderStatus(id) {
    return this.get(`/orders/${id}/status`);
  }

  async cancelOrder(id, reason = '') {
    return this.put(`/orders/${id}/cancel`, { raison: reason });
  }

  async getOrderStats() {
    return this.get('/orders/stats');
  }

  // Méthodes pour le profil client
  async updateClientProfile(profileData) {
    return this.put('/clients/profile', profileData);
  }

  async deleteClientAccount(password) {
    return this.delete('/clients/account', { motDePasse: password });
  }

  // Méthodes d'administration
  async getDashboardStats() {
    return this.get('/admin/dashboard');
  }

  async getAdminProducts(params = {}) {
    return this.get('/admin/products', params);
  }

  async createProduct(productData) {
    return this.post('/admin/products', productData);
  }

  async updateProduct(id, productData) {
    return this.put(`/admin/products/${id}`, productData);
  }

  async deleteProduct(id) {
    return this.delete(`/admin/products/${id}`);
  }

  async getAdminOrders(params = {}) {
    return this.get('/admin/orders', params);
  }

  async updateOrderStatus(id, statusData) {
    return this.put(`/admin/orders/${id}/status`, statusData);
  }

  async getLowStockProducts() {
    return this.get('/admin/stock/low');
  }

  async getStockStats() {
    return this.get('/admin/stock/stats');
  }

  async updateStock(productId, stockData) {
    return this.put(`/admin/stock/${productId}`, stockData);
  }

  async getStockHistory(productId, params = {}) {
    return this.get(`/admin/stock/${productId}/history`, params);
  }

  async getClients(params = {}) {
    return this.get('/admin/clients', params);
  }
}

// Instance singleton du service API
const apiService = new ApiService();

export default apiService;
