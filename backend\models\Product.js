const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  nom: {
    type: String,
    required: [true, 'Le nom du produit est requis'],
    trim: true,
    maxlength: [200, 'Le nom ne peut pas dépasser 200 caractères']
  },
  description: {
    type: String,
    required: [true, 'La description est requise'],
    trim: true,
    maxlength: [1000, 'La description ne peut pas dépasser 1000 caractères']
  },
  prix: {
    type: Number,
    required: [true, 'Le prix est requis'],
    min: [0, 'Le prix ne peut pas être négatif'],
    validate: {
      validator: function(v) {
        return v > 0;
      },
      message: 'Le prix doit être supérieur à 0'
    }
  },
  categorie: {
    type: String,
    required: [true, 'La catégorie est requise'],
    enum: [
      'fruits',
      'legumes',
      'produits_laitiers',
      'viandes',
      'poissons',
      'cereales',
      'epices',
      'boissons',
      'conserves',
      'surgeles',
      'autres'
    ]
  },
  stock: {
    type: Number,
    required: [true, 'Le stock est requis'],
    min: [0, 'Le stock ne peut pas être négatif'],
    default: 0
  },
  seuilAlerte: {
    type: Number,
    default: 10,
    min: [0, 'Le seuil d\'alerte ne peut pas être négatif']
  },
  unite: {
    type: String,
    required: [true, 'L\'unité est requise'],
    enum: ['kg', 'g', 'l', 'ml', 'piece', 'boite', 'paquet'],
    default: 'kg'
  },
  image: {
    type: String,
    required: [true, 'L\'URL de l\'image est requise'],
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
      },
      message: 'URL d\'image invalide'
    }
  },
  images: [{
    type: String,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
      },
      message: 'URL d\'image invalide'
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  dateAjout: {
    type: Date,
    default: Date.now
  },
  ajouteParAdmin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  specifications: {
    origine: String,
    marque: String,
    dateExpiration: Date,
    conditions_stockage: String
  },
  promotion: {
    isActive: {
      type: Boolean,
      default: false
    },
    pourcentage: {
      type: Number,
      min: 0,
      max: 100
    },
    dateDebut: Date,
    dateFin: Date
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
productSchema.index({ nom: 'text', description: 'text' });
productSchema.index({ categorie: 1 });
productSchema.index({ prix: 1 });
productSchema.index({ stock: 1 });
productSchema.index({ isActive: 1 });
productSchema.index({ dateAjout: -1 });

// Méthode pour vérifier si le stock est faible
productSchema.methods.isLowStock = function() {
  return this.stock <= this.seuilAlerte;
};

// Méthode pour calculer le prix avec promotion
productSchema.methods.getPrixAvecPromotion = function() {
  if (this.promotion.isActive && 
      this.promotion.dateDebut <= new Date() && 
      this.promotion.dateFin >= new Date()) {
    return this.prix * (1 - this.promotion.pourcentage / 100);
  }
  return this.prix;
};

// Méthode pour vérifier la disponibilité
productSchema.methods.isAvailable = function(quantite = 1) {
  return this.isActive && this.stock >= quantite;
};

// Méthode pour réduire le stock
productSchema.methods.reduireStock = function(quantite) {
  if (this.stock >= quantite) {
    this.stock -= quantite;
    return this.save();
  } else {
    throw new Error('Stock insuffisant');
  }
};

// Méthode pour augmenter le stock
productSchema.methods.augmenterStock = function(quantite) {
  this.stock += quantite;
  return this.save();
};

// Virtual pour le statut du stock
productSchema.virtual('stockStatus').get(function() {
  if (this.stock === 0) return 'rupture';
  if (this.stock <= this.seuilAlerte) return 'faible';
  return 'normal';
});

// Inclure les virtuals dans la sérialisation JSON
productSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Product', productSchema);
