{"version": 3, "sources": ["RNGestureHandlerModule.ts"], "names": ["<PERSON><PERSON><PERSON>"], "mappings": "AAAA;AACA;AAEA,OAAOA,MAAP,MAAmB,sCAAnB;AACA,eAAeA,MAAf", "sourcesContent": ["// Reexport the native module spec used by codegen. The relevant files are inluded on Android\n// to ensure the compatibility with the old arch, while iOS doesn't require those at all.\n\nimport Module from './specs/NativeRNGestureHandlerModule';\nexport default Module;\n"]}