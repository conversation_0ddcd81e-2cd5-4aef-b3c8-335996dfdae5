const jwt = require('jsonwebtoken');
const Client = require('../models/Client');
const Admin = require('../models/Admin');

// Middleware d'authentification générale
const authenticate = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Token d\'accès requis' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Vérifier si c'est un client ou un admin
    let user;
    if (decoded.type === 'client') {
      user = await Client.findById(decoded.id).select('-motDePasse');
      if (!user || !user.isActive) {
        return res.status(401).json({ message: 'Client non trouvé ou inactif' });
      }
    } else if (decoded.type === 'admin') {
      user = await Admin.findById(decoded.id).select('-motDePasse');
      if (!user || !user.isActive) {
        return res.status(401).json({ message: 'Admin non trouvé ou inactif' });
      }
    } else {
      return res.status(401).json({ message: 'Type d\'utilisateur invalide' });
    }

    req.user = user;
    req.userType = decoded.type;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Token invalide' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expiré' });
    }
    return res.status(500).json({ message: 'Erreur d\'authentification' });
  }
};

// Middleware pour authentifier uniquement les clients
const authenticateClient = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Token d\'accès requis' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'client') {
      return res.status(403).json({ message: 'Accès réservé aux clients' });
    }

    const client = await Client.findById(decoded.id).select('-motDePasse');
    if (!client || !client.isActive) {
      return res.status(401).json({ message: 'Client non trouvé ou inactif' });
    }

    req.user = client;
    req.userType = 'client';
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Token invalide' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expiré' });
    }
    return res.status(500).json({ message: 'Erreur d\'authentification' });
  }
};

// Middleware pour authentifier uniquement les admins
const authenticateAdmin = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Token d\'accès requis' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'admin') {
      return res.status(403).json({ message: 'Accès réservé aux administrateurs' });
    }

    const admin = await Admin.findById(decoded.id).select('-motDePasse');
    if (!admin || !admin.isActive) {
      return res.status(401).json({ message: 'Admin non trouvé ou inactif' });
    }

    req.user = admin;
    req.userType = 'admin';
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Token invalide' });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expiré' });
    }
    return res.status(500).json({ message: 'Erreur d\'authentification' });
  }
};

// Middleware pour vérifier les permissions d'admin
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (req.userType !== 'admin') {
      return res.status(403).json({ message: 'Accès réservé aux administrateurs' });
    }

    if (!req.user.hasPermission(permission)) {
      return res.status(403).json({ 
        message: `Permission '${permission}' requise`,
        userPermissions: req.user.permissions 
      });
    }

    next();
  };
};

// Middleware pour vérifier le rôle super admin
const requireSuperAdmin = (req, res, next) => {
  if (req.userType !== 'admin' || req.user.role !== 'super_admin') {
    return res.status(403).json({ message: 'Accès réservé aux super administrateurs' });
  }
  next();
};

module.exports = {
  authenticate,
  authenticateClient,
  authenticateAdmin,
  requirePermission,
  requireSuperAdmin
};
