{"version": 3, "sources": ["handlersRegistry.ts"], "names": ["handlerIDToTag", "gestures", "Map", "oldHandlers", "testIDs", "registerHandler", "handlerTag", "handler", "testID", "set", "registerOldGestureHandler", "unregisterOldGestureHandler", "delete", "unregister<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "get", "findOldGestureHandler", "findHandlerByTestID", "undefined"], "mappings": ";;;;;;;;;;;;;;AAAA;;AAIO,MAAMA,cAAsC,GAAG,EAA/C;;AACP,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;AACA,MAAMC,WAAW,GAAG,IAAID,GAAJ,EAApB;AACA,MAAME,OAAO,GAAG,IAAIF,GAAJ,EAAhB;;AAEO,SAASG,eAAT,CACLC,UADK,EAELC,OAFK,EAGLC,MAHK,EAIL;AACAP,EAAAA,QAAQ,CAACQ,GAAT,CAAaH,UAAb,EAAyBC,OAAzB;;AACA,MAAI,2BAAeC,MAAnB,EAA2B;AACzBJ,IAAAA,OAAO,CAACK,GAAR,CAAYD,MAAZ,EAAoBF,UAApB;AACD;AACF;;AAEM,SAASI,yBAAT,CACLJ,UADK,EAELC,OAFK,EAGL;AACAJ,EAAAA,WAAW,CAACM,GAAZ,CAAgBH,UAAhB,EAA4BC,OAA5B;AACD;;AAEM,SAASI,2BAAT,CAAqCL,UAArC,EAAyD;AAC9DH,EAAAA,WAAW,CAACS,MAAZ,CAAmBN,UAAnB;AACD;;AAEM,SAASO,iBAAT,CAA2BP,UAA3B,EAA+CE,MAA/C,EAAgE;AACrEP,EAAAA,QAAQ,CAACW,MAAT,CAAgBN,UAAhB;;AACA,MAAI,2BAAeE,MAAnB,EAA2B;AACzBJ,IAAAA,OAAO,CAACQ,MAAR,CAAeJ,MAAf;AACD;AACF;;AAEM,SAASM,WAAT,CAAqBR,UAArB,EAAyC;AAC9C,SAAOL,QAAQ,CAACc,GAAT,CAAaT,UAAb,CAAP;AACD;;AAEM,SAASU,qBAAT,CAA+BV,UAA/B,EAAmD;AACxD,SAAOH,WAAW,CAACY,GAAZ,CAAgBT,UAAhB,CAAP;AACD;;AAEM,SAASW,mBAAT,CAA6BT,MAA7B,EAA6C;AAClD,QAAMF,UAAU,GAAGF,OAAO,CAACW,GAAR,CAAYP,MAAZ,CAAnB;;AACA,MAAIF,UAAU,KAAKY,SAAnB,EAA8B;AAAA;;AAC5B,2BAAOJ,WAAW,CAACR,UAAD,CAAlB,uDAAkC,IAAlC;AACD;;AACD,SAAO,IAAP;AACD", "sourcesContent": ["import { isTestEnv } from '../utils';\nimport { GestureType } from './gestures/gesture';\nimport { GestureEvent, HandlerStateChangeEvent } from './gestureHandlerCommon';\n\nexport const handlerIDToTag: Record<string, number> = {};\nconst gestures = new Map<number, GestureType>();\nconst oldHandlers = new Map<number, GestureHandlerCallbacks>();\nconst testIDs = new Map<string, number>();\n\nexport function registerHandler(\n  handlerTag: number,\n  handler: GestureType,\n  testID?: string\n) {\n  gestures.set(handlerTag, handler);\n  if (isTestEnv() && testID) {\n    testIDs.set(testID, handlerTag);\n  }\n}\n\nexport function registerOldGestureHandler(\n  handlerTag: number,\n  handler: GestureHandlerCallbacks\n) {\n  oldHandlers.set(handlerTag, handler);\n}\n\nexport function unregisterOldGestureHandler(handlerTag: number) {\n  oldHandlers.delete(handlerTag);\n}\n\nexport function unregisterHandler(handlerTag: number, testID?: string) {\n  gestures.delete(handlerTag);\n  if (isTestEnv() && testID) {\n    testIDs.delete(testID);\n  }\n}\n\nexport function findHandler(handlerTag: number) {\n  return gestures.get(handlerTag);\n}\n\nexport function findOldGestureHandler(handlerTag: number) {\n  return oldHandlers.get(handlerTag);\n}\n\nexport function findHandlerByTestID(testID: string) {\n  const handlerTag = testIDs.get(testID);\n  if (handlerTag !== undefined) {\n    return findHandler(handlerTag) ?? null;\n  }\n  return null;\n}\n\nexport interface GestureHandlerCallbacks {\n  onGestureEvent: (event: GestureEvent<any>) => void;\n  onGestureStateChange: (event: HandlerStateChangeEvent<any>) => void;\n}\n"]}