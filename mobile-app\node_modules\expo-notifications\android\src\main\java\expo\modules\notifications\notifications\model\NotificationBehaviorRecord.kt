package expo.modules.notifications.notifications.model

import android.os.Parcelable
import expo.modules.kotlin.records.Field
import expo.modules.kotlin.records.Record
import expo.modules.notifications.notifications.enums.NotificationPriority
import kotlinx.parcelize.Parcelize

@Parcelize
data class NotificationBehaviorRecord(
  @Field val shouldShowAlert: Boolean = false,
  @Field val shouldShowBanner: Boolean = false,
  @Field val shouldShowList: <PERSON>olean = false,
  @Field val shouldPlaySound: <PERSON>olean = false,
  @Field val shouldSetBadge: <PERSON><PERSON><PERSON> = false,
  @Field val priority: String? = null
) : Record, Parcelable {

  val priorityOverride: NotificationPriority? get() {
    return priority?.let { NotificationPriority.fromEnumValue(it) }
  }

  val shouldPresentAlert: Boolean get() {
    return shouldShowBanner || shouldShowList || shouldShowAlert
  }
}
