const http = require('http');
const url = require('url');

const PORT = 5000;

// Fonction pour gérer CORS
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// Fonction pour parser le body JSON
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Serveur HTTP simple
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Gérer CORS
  setCorsHeaders(res);
  
  // Répondre aux requêtes OPTIONS (preflight)
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`${method} ${path}`);

  try {
    // Route de santé
    if (method === 'GET' && path === '/api/health') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'OK',
        message: 'API GrosMarket fonctionne !',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }));
      return;
    }

    // Route de connexion admin
    if (method === 'POST' && path === '/api/auth/login/admin') {
      const body = await parseBody(req);
      console.log('📝 Tentative de connexion admin:', body);

      const { email, motDePasse } = body;

      if (email === '<EMAIL>' && motDePasse === 'admin123456') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: 'Connexion réussie',
          user: {
            id: '1',
            nom: 'Administrateur',
            email: email,
            role: 'admin'
          },
          token: 'fake-jwt-token-for-test'
        }));
      } else {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          message: 'Email ou mot de passe incorrect'
        }));
      }
      return;
    }

    // Route de connexion client
    if (method === 'POST' && path === '/api/auth/login/client') {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: 'Aucun client configuré pour le test. Utilisez le compte admin.'
      }));
      return;
    }

    // Route par défaut
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      message: 'Route non trouvée',
      path: path
    }));

  } catch (error) {
    console.error('❌ Erreur:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      message: 'Erreur serveur'
    }));
  }
});

// Gestion des erreurs du serveur
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Le port ${PORT} est déjà utilisé`);
    console.log('💡 Essayez de fermer les autres serveurs ou changez le port');
  } else {
    console.error('❌ Erreur serveur:', error);
  }
});

// Démarrage du serveur
server.listen(PORT, () => {
  console.log('🚀 Serveur minimal GrosMarket démarré');
  console.log(`📡 API disponible sur http://localhost:${PORT}`);
  console.log('🔧 Routes disponibles:');
  console.log('   GET  /api/health');
  console.log('   POST /api/auth/login/admin');
  console.log('   POST /api/auth/login/client');
  console.log('');
  console.log('👨‍💼 Compte admin de test:');
  console.log('   Email: <EMAIL>');
  console.log('   Mot de passe: admin123456');
  console.log('');
  console.log('🌐 Testez avec: http://localhost:3001');
});
