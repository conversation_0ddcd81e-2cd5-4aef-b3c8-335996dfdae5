const Product = require('../models/Product');
const StockHistory = require('../models/StockHistory');
const stockService = require('../services/stockService');

class ProductController {

  // Obtenir tous les produits avec filtres et pagination
  async getProducts(req, res) {
    try {
      const {
        q,
        categorie,
        prixMin,
        prixMax,
        page = 1,
        limit = 10,
        sortBy = 'dateAjout',
        sortOrder = 'desc'
      } = req.query;

      // Construction de la requête
      const query = { isActive: true };

      // Recherche textuelle
      if (q) {
        query.$text = { $search: q };
      }

      // Filtre par catégorie
      if (categorie) {
        query.categorie = categorie;
      }

      // Filtre par prix
      if (prixMin || prixMax) {
        query.prix = {};
        if (prixMin) query.prix.$gte = parseFloat(prixMin);
        if (prixMax) query.prix.$lte = parseFloat(prixMax);
      }

      // Options de tri
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Pagination
      const skip = (page - 1) * limit;

      // Exécution de la requête
      const [products, total] = await Promise.all([
        Product.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('ajouteParAdmin', 'nom email'),
        Product.countDocuments(query)
      ]);

      // Calcul des métadonnées de pagination
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      res.json({
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage,
          hasPrevPage
        },
        filters: {
          q,
          categorie,
          prixMin,
          prixMax,
          sortBy,
          sortOrder
        }
      });

    } catch (error) {
      console.error('Erreur récupération produits:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des produits',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir un produit par ID
  async getProductById(req, res) {
    try {
      const { id } = req.params;

      const product = await Product.findOne({ _id: id, isActive: true })
        .populate('ajouteParAdmin', 'nom email');

      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      res.json(product);

    } catch (error) {
      console.error('Erreur récupération produit:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération du produit',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir les catégories disponibles
  async getCategories(req, res) {
    try {
      const categories = await Product.distinct('categorie', { isActive: true });
      
      // Compter les produits par catégorie
      const categoriesWithCount = await Promise.all(
        categories.map(async (categorie) => {
          const count = await Product.countDocuments({ 
            categorie, 
            isActive: true 
          });
          return { categorie, count };
        })
      );

      res.json(categoriesWithCount.sort((a, b) => b.count - a.count));

    } catch (error) {
      console.error('Erreur récupération catégories:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des catégories',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir les produits en promotion
  async getPromotionalProducts(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const skip = (page - 1) * limit;

      const now = new Date();
      const query = {
        isActive: true,
        'promotion.isActive': true,
        'promotion.dateDebut': { $lte: now },
        'promotion.dateFin': { $gte: now }
      };

      const [products, total] = await Promise.all([
        Product.find(query)
          .sort({ 'promotion.pourcentage': -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .populate('ajouteParAdmin', 'nom email'),
        Product.countDocuments(query)
      ]);

      // Ajouter le prix avec promotion
      const productsWithPromoPrice = products.map(product => ({
        ...product.toJSON(),
        prixPromotion: product.getPrixAvecPromotion()
      }));

      const totalPages = Math.ceil(total / limit);

      res.json({
        products: productsWithPromoPrice,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });

    } catch (error) {
      console.error('Erreur récupération produits en promotion:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des produits en promotion',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Vérifier la disponibilité d'un produit
  async checkAvailability(req, res) {
    try {
      const { id } = req.params;
      const { quantite = 1 } = req.query;

      const product = await Product.findOne({ _id: id, isActive: true });

      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      const isAvailable = product.isAvailable(parseInt(quantite));
      const maxQuantiteDisponible = product.stock;

      res.json({
        produitId: product._id,
        nom: product.nom,
        quantiteDemandee: parseInt(quantite),
        isAvailable,
        stockDisponible: maxQuantiteDisponible,
        message: isAvailable 
          ? 'Produit disponible' 
          : `Stock insuffisant. Maximum disponible: ${maxQuantiteDisponible}`
      });

    } catch (error) {
      console.error('Erreur vérification disponibilité:', error);
      res.status(500).json({
        message: 'Erreur lors de la vérification de disponibilité',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Recherche avancée de produits
  async searchProducts(req, res) {
    try {
      const { q } = req.query;

      if (!q || q.trim().length < 2) {
        return res.status(400).json({
          message: 'Le terme de recherche doit contenir au moins 2 caractères'
        });
      }

      // Recherche avec score de pertinence
      const products = await Product.find(
        { 
          $text: { $search: q },
          isActive: true 
        },
        { score: { $meta: 'textScore' } }
      )
      .sort({ score: { $meta: 'textScore' } })
      .limit(20)
      .populate('ajouteParAdmin', 'nom email');

      // Suggestions basées sur les catégories des résultats
      const categories = [...new Set(products.map(p => p.categorie))];
      const suggestions = await Product.find({
        categorie: { $in: categories },
        isActive: true,
        _id: { $nin: products.map(p => p._id) }
      })
      .limit(5)
      .select('nom categorie prix image');

      res.json({
        query: q,
        results: products,
        suggestions,
        totalFound: products.length
      });

    } catch (error) {
      console.error('Erreur recherche produits:', error);
      res.status(500).json({
        message: 'Erreur lors de la recherche',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }

  // Obtenir les produits similaires
  async getSimilarProducts(req, res) {
    try {
      const { id } = req.params;
      const { limit = 5 } = req.query;

      const product = await Product.findById(id);
      if (!product) {
        return res.status(404).json({
          message: 'Produit non trouvé'
        });
      }

      // Rechercher des produits similaires par catégorie et prix
      const prixMin = product.prix * 0.7;
      const prixMax = product.prix * 1.3;

      const similarProducts = await Product.find({
        _id: { $ne: product._id },
        categorie: product.categorie,
        prix: { $gte: prixMin, $lte: prixMax },
        isActive: true
      })
      .limit(parseInt(limit))
      .select('nom prix image categorie stock')
      .sort({ dateAjout: -1 });

      res.json({
        productId: id,
        similarProducts
      });

    } catch (error) {
      console.error('Erreur récupération produits similaires:', error);
      res.status(500).json({
        message: 'Erreur lors de la récupération des produits similaires',
        error: process.env.NODE_ENV === 'development' ? error.message : {}
      });
    }
  }
}

module.exports = new ProductController();
