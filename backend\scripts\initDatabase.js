const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const Product = require('../models/Product');
require('dotenv').config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connecté');
  } catch (error) {
    console.error('❌ Erreur connexion MongoDB:', error.message);
    process.exit(1);
  }
};

const createDefaultAdmin = async () => {
  try {
    // Vérifier si un super admin existe déjà
    const existingAdmin = await Admin.findOne({ role: 'super_admin' });
    
    if (existingAdmin) {
      console.log('✅ Super admin existe déjà:', existingAdmin.email);
      return existingAdmin;
    }

    // Créer le super admin par défaut
    const defaultAdmin = new Admin({
      nom: 'Super Administrateur',
      email: '<EMAIL>',
      motDePasse: 'admin123456', // À changer en production !
      role: 'super_admin',
      permissions: [
        'manage_products',
        'manage_orders',
        'manage_clients',
        'manage_stock',
        'view_analytics',
        'manage_admins'
      ]
    });

    await defaultAdmin.save();
    console.log('✅ Super admin créé avec succès');
    console.log('📧 Email:', defaultAdmin.email);
    console.log('🔑 Mot de passe: admin123456');
    console.log('⚠️  IMPORTANT: Changez le mot de passe après la première connexion !');
    
    return defaultAdmin;
  } catch (error) {
    console.error('❌ Erreur création admin:', error.message);
    throw error;
  }
};

const createSampleProducts = async (adminId) => {
  try {
    // Vérifier si des produits existent déjà
    const existingProducts = await Product.countDocuments();
    
    if (existingProducts > 0) {
      console.log('✅ Des produits existent déjà dans la base');
      return;
    }

    const sampleProducts = [
      {
        nom: 'Pommes Golden',
        description: 'Pommes Golden fraîches et croquantes, parfaites pour tous les usages culinaires.',
        prix: 2.50,
        categorie: 'fruits',
        stock: 100,
        seuilAlerte: 20,
        unite: 'kg',
        image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'France',
          conditions_stockage: 'Conserver au frais'
        }
      },
      {
        nom: 'Carottes Bio',
        description: 'Carottes biologiques cultivées sans pesticides, riches en vitamines.',
        prix: 1.80,
        categorie: 'legumes',
        stock: 75,
        seuilAlerte: 15,
        unite: 'kg',
        image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'France',
          marque: 'Bio Local'
        }
      },
      {
        nom: 'Lait Entier',
        description: 'Lait entier frais de vaches françaises, riche en calcium.',
        prix: 1.20,
        categorie: 'produits_laitiers',
        stock: 50,
        seuilAlerte: 10,
        unite: 'l',
        image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'France',
          conditions_stockage: 'Réfrigérer entre 0°C et 4°C'
        }
      },
      {
        nom: 'Riz Basmati',
        description: 'Riz basmati de qualité supérieure, grains longs et parfumés.',
        prix: 3.50,
        categorie: 'cereales',
        stock: 200,
        seuilAlerte: 30,
        unite: 'kg',
        image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'Inde',
          conditions_stockage: 'Conserver au sec'
        }
      },
      {
        nom: 'Saumon Frais',
        description: 'Filets de saumon frais, pêche responsable, riche en oméga-3.',
        prix: 15.90,
        categorie: 'poissons',
        stock: 25,
        seuilAlerte: 5,
        unite: 'kg',
        image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'Norvège',
          conditions_stockage: 'Conserver entre 0°C et 2°C'
        }
      },
      {
        nom: 'Tomates Cerises',
        description: 'Tomates cerises sucrées et juteuses, parfaites pour les salades.',
        prix: 4.20,
        categorie: 'legumes',
        stock: 40,
        seuilAlerte: 8,
        unite: 'kg',
        image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'Espagne',
          conditions_stockage: 'Conserver à température ambiante'
        }
      },
      {
        nom: 'Huile d\'Olive Extra Vierge',
        description: 'Huile d\'olive extra vierge première pression à froid.',
        prix: 8.50,
        categorie: 'autres',
        stock: 60,
        seuilAlerte: 12,
        unite: 'l',
        image: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'Italie',
          conditions_stockage: 'Conserver à l\'abri de la lumière'
        }
      },
      {
        nom: 'Yaourt Nature',
        description: 'Yaourt nature onctueux, sans additifs artificiels.',
        prix: 0.80,
        categorie: 'produits_laitiers',
        stock: 80,
        seuilAlerte: 15,
        unite: 'piece',
        image: 'https://images.unsplash.com/photo-1571212515416-fca88c6e8b5c?w=500',
        ajouteParAdmin: adminId,
        specifications: {
          origine: 'France',
          conditions_stockage: 'Réfrigérer'
        }
      }
    ];

    await Product.insertMany(sampleProducts);
    console.log(`✅ ${sampleProducts.length} produits d'exemple créés`);
    
  } catch (error) {
    console.error('❌ Erreur création produits:', error.message);
    throw error;
  }
};

const initDatabase = async () => {
  try {
    console.log('🚀 Initialisation de la base de données...');
    
    await connectDB();
    
    const admin = await createDefaultAdmin();
    await createSampleProducts(admin._id);
    
    console.log('✅ Base de données initialisée avec succès !');
    console.log('\n📋 Résumé:');
    console.log('- Super admin créé');
    console.log('- Produits d\'exemple ajoutés');
    console.log('\n🔗 Vous pouvez maintenant:');
    console.log('1. Démarrer le serveur: npm run dev');
    console.log('2. Vous <NAME_EMAIL> / admin123456');
    console.log('3. Changer le mot de passe admin');
    console.log('4. Ajouter vos propres produits');
    
  } catch (error) {
    console.error('❌ Erreur initialisation:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Connexion fermée');
    process.exit(0);
  }
};

// Exécuter le script si appelé directement
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase, createDefaultAdmin, createSampleProducts };
