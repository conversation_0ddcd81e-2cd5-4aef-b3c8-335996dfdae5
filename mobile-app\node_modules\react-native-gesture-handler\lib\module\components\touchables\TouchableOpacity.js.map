{"version": 3, "sources": ["TouchableOpacity.tsx"], "names": ["Animated", "Easing", "StyleSheet", "View", "GenericTouchable", "TOUCHABLE_STATE", "React", "Component", "TouchableOpacity", "childStyle", "flatten", "props", "style", "opacity", "valueOf", "Value", "getChildStyleOpacityWithDefault", "value", "duration", "timing", "toValue", "easing", "inOut", "quad", "useNativeDriver", "useNativeAnimations", "start", "_from", "to", "BEGAN", "setOpacityTo", "activeOpacity", "UNDETERMINED", "MOVED_OUTSIDE", "render", "rest", "onStateChange", "children", "defaultProps"], "mappings": ";;;;AAAA,SACEA,QADF,EAEEC,MAFF,EAGEC,UAHF,EAIEC,IAJF,QAMO,cANP;AAOA,OAAOC,gBAAP,IAA2BC,eAA3B,QAAkD,oBAAlD;AAEA,OAAO,KAAKC,KAAZ,MAAuB,OAAvB;AACA,SAASC,SAAT,QAA0B,OAA1B;AAEA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,gBAAN,SAA+BD,SAA/B,CAAgE;AAAA;AAAA;;AAAA,6DAO3C,MAAM;AACtC,YAAME,UAAU,GAAGP,UAAU,CAACQ,OAAX,CAAmB,KAAKC,KAAL,CAAWC,KAA9B,KAAwC,EAA3D;AACA,aAAOH,UAAU,CAACI,OAAX,IAAsB,IAAtB,GACH,CADG,GAEFJ,UAAU,CAACI,OAAX,CAAmBC,OAAnB,EAFL;AAGD,KAZ4E;;AAAA,qCAcnE,IAAId,QAAQ,CAACe,KAAb,CAAmB,KAAKC,+BAAL,EAAnB,CAdmE;;AAAA,0CAgB9D,CAACC,KAAD,EAAgBC,QAAhB,KAAqC;AAAA;;AAClDlB,MAAAA,QAAQ,CAACmB,MAAT,CAAgB,KAAKN,OAArB,EAA8B;AAC5BO,QAAAA,OAAO,EAAEH,KADmB;AAE5BC,QAAAA,QAAQ,EAAEA,QAFkB;AAG5BG,QAAAA,MAAM,EAAEpB,MAAM,CAACqB,KAAP,CAAarB,MAAM,CAACsB,IAApB,CAHoB;AAI5BC,QAAAA,eAAe,2BAAE,KAAKb,KAAL,CAAWc,mBAAb,yEAAoC;AAJvB,OAA9B,EAKGC,KALH;AAMD,KAvB4E;;AAAA,2CAyB7D,CAACC,KAAD,EAAgBC,EAAhB,KAA+B;AAC7C,UAAIA,EAAE,KAAKvB,eAAe,CAACwB,KAA3B,EAAkC;AAChC,aAAKC,YAAL,CAAkB,KAAKnB,KAAL,CAAWoB,aAA7B,EAA6C,CAA7C;AACD,OAFD,MAEO,IACLH,EAAE,KAAKvB,eAAe,CAAC2B,YAAvB,IACAJ,EAAE,KAAKvB,eAAe,CAAC4B,aAFlB,EAGL;AACA,aAAKH,YAAL,CAAkB,KAAKd,+BAAL,EAAlB,EAA0D,GAA1D;AACD;AACF,KAlC4E;AAAA;;AAoC7EkB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEtB,MAAAA,KAAK,GAAG,EAAV;AAAc,SAAGuB;AAAjB,QAA0B,KAAKxB,KAArC;AACA,wBACE,oBAAC,gBAAD,eACMwB,IADN;AAEE,MAAA,KAAK,EAAE,CACLvB,KADK,EAEL;AACEC,QAAAA,OAAO,EAAE,KAAKA,OADhB,CAC8C;;AAD9C,OAFK,CAFT;AAQE,MAAA,aAAa,EAAE,KAAKuB;AARtB,QASG,KAAKzB,KAAL,CAAW0B,QAAX,GAAsB,KAAK1B,KAAL,CAAW0B,QAAjC,gBAA4C,oBAAC,IAAD,OAT/C,CADF;AAaD;;AAnD4E;;gBAA1D7B,gB,kBACG,EACpB,GAAGJ,gBAAgB,CAACkC,YADA;AAEpBP,EAAAA,aAAa,EAAE;AAFK,C", "sourcesContent": ["import {\n  Animated,\n  Easing,\n  StyleSheet,\n  View,\n  TouchableOpacityProps as RNTouchableOpacityProps,\n} from 'react-native';\nimport GenericTouchable, { TOUCHABLE_STATE } from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\nimport * as React from 'react';\nimport { Component } from 'react';\n\n/**\n * @deprecated TouchableOpacity will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nexport type TouchableOpacityProps = RNTouchableOpacityProps &\n  GenericTouchableProps & {\n    useNativeAnimations?: boolean;\n  };\n\n/**\n * @deprecated TouchableOpacity will be removed in the future version of Gesture Handler. Use Pressable instead.\n *\n * TouchableOpacity bases on timing animation which has been used in RN's core\n */\nexport default class TouchableOpacity extends Component<TouchableOpacityProps> {\n  static defaultProps = {\n    ...GenericTouchable.defaultProps,\n    activeOpacity: 0.2,\n  };\n\n  // Opacity is 1 one by default but could be overwritten\n  getChildStyleOpacityWithDefault = () => {\n    const childStyle = StyleSheet.flatten(this.props.style) || {};\n    return childStyle.opacity == null\n      ? 1\n      : (childStyle.opacity.valueOf() as number);\n  };\n\n  opacity = new Animated.Value(this.getChildStyleOpacityWithDefault());\n\n  setOpacityTo = (value: number, duration: number) => {\n    Animated.timing(this.opacity, {\n      toValue: value,\n      duration: duration,\n      easing: Easing.inOut(Easing.quad),\n      useNativeDriver: this.props.useNativeAnimations ?? true,\n    }).start();\n  };\n\n  onStateChange = (_from: number, to: number) => {\n    if (to === TOUCHABLE_STATE.BEGAN) {\n      this.setOpacityTo(this.props.activeOpacity!, 0);\n    } else if (\n      to === TOUCHABLE_STATE.UNDETERMINED ||\n      to === TOUCHABLE_STATE.MOVED_OUTSIDE\n    ) {\n      this.setOpacityTo(this.getChildStyleOpacityWithDefault(), 150);\n    }\n  };\n\n  render() {\n    const { style = {}, ...rest } = this.props;\n    return (\n      <GenericTouchable\n        {...rest}\n        style={[\n          style,\n          {\n            opacity: this.opacity as unknown as number, // TODO: fix this\n          },\n        ]}\n        onStateChange={this.onStateChange}>\n        {this.props.children ? this.props.children : <View />}\n      </GenericTouchable>\n    );\n  }\n}\n"]}