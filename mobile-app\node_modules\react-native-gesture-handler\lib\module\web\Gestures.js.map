{"version": 3, "sources": ["Gestures.ts"], "names": ["PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "Gestures", "HammerGestures"], "mappings": "AAAA;AACA,OAAOA,iBAAP,MAA8B,8BAA9B;AACA,OAAOC,iBAAP,MAA8B,8BAA9B;AACA,OAAOC,uBAAP,MAAoC,oCAApC;AACA,OAAOC,mBAAP,MAAgC,gCAAhC;AACA,OAAOC,sBAAP,MAAmC,mCAAnC;AACA,OAAOC,mBAAP,MAAgC,gCAAhC;AACA,OAAOC,wBAAP,MAAqC,qCAArC;AACA,OAAOC,oBAAP,MAAiC,iCAAjC;AACA,OAAOC,mBAAP,MAAgC,gCAAhC,C,CAEA;;AACA,OAAOC,8BAAP,MAA2C,wCAA3C;AACA,OAAOC,uBAAP,MAAoC,iCAApC;AACA,OAAOC,uBAAP,MAAoC,iCAApC;AACA,OAAOC,6BAAP,MAA0C,uCAA1C;AACA,OAAOC,yBAAP,MAAsC,mCAAtC;AACA,OAAOC,4BAAP,MAAyC,sCAAzC;AACA,OAAOC,yBAAP,MAAsC,mCAAtC;AAEA,OAAO,MAAMC,QAAQ,GAAG;AACtBV,EAAAA,wBADsB;AAEtBN,EAAAA,iBAFsB;AAGtBC,EAAAA,iBAHsB;AAItBC,EAAAA,uBAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA,sBANsB;AAOtBC,EAAAA,mBAPsB;AAQtBE,EAAAA,oBARsB;AAStBC,EAAAA;AATsB,CAAjB;AAYP,OAAO,MAAMS,cAAc,GAAG;AAC5BX,EAAAA,wBAAwB,EAAEG,8BADE;AAE5BT,EAAAA,iBAAiB,EAAEU,uBAFS;AAG5BT,EAAAA,iBAAiB,EAAEU,uBAHS;AAI5BT,EAAAA,uBAAuB,EAAEU,6BAJG;AAK5BT,EAAAA,mBAAmB,EAAEU,yBALO;AAM5BT,EAAAA,sBAAsB,EAAEU,4BANI;AAO5BT,EAAAA,mBAAmB,EAAEU;AAPO,CAAvB", "sourcesContent": ["// Gesture Handlers\nimport PanGestureHandler from './handlers/PanGestureHandler';\nimport TapGestureHandler from './handlers/TapGestureHandler';\nimport LongPressGestureHandler from './handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './handlers/PinchGestureHandler';\nimport RotationGestureHandler from './handlers/RotationGestureHandler';\nimport FlingGestureHandler from './handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './handlers/ManualGestureHandler';\nimport HoverGestureHandler from './handlers/HoverGestureHandler';\n\n// Hammer Handlers\nimport HammerNativeViewGestureHandler from '../web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from '../web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from '../web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from '../web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from '../web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from '../web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from '../web_hammer/FlingGestureHandler';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n  HoverGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n"]}